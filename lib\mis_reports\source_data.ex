defmodule MisReports.SourceData do
  @moduledoc """
  The SourceData context.
  """

  import Ecto.Query, warn: false
  alias MisReports.Repo

  alias MisReports.SourceData.CustSegment
  alias MisReports.Utilities
  alias MisReports.SourceData.DebtorsBookAnalysis

  @doc """
  Returns the list of tbl_cust_segmentation.

  ## Examples

      iex> list_tbl_cust_segmentation()
      [%CustSegment{}, ...]

  """
  def list_tbl_cust_segmentation do
    Repo.all(CustSegment)
  end

  def gbm_by_file(filename) do
    CustSegment
    |> where(src_filename: ^filename)
    |> Repo.all()
  end

  def gbm_by_month(month) do
    CustSegment
    |> where(month: ^month)
    |> Repo.all()
  end

  # def get_gut_sap_number(month) do
  #   CustSegment
  #     |> join(:left, [a], b in "tbl_trial_balance_entries", on: a.sap_gl_acc_no == b.gl_no)
  #     |> where([a, b], a.month == ^month)
  #     |> select([a, b], %{
  #       sap_gl_acc_no: a.sap_gl_acc_no,
  #       acc_bal_in_lcy: coalesce(a.acc_bal_in_lcy, 0),
  #       actual_this_year: coalesce(b.actual_this_year, 0),
  #       gl_desc: b.gl_desc,
  #       gl_no: b.gl_no,
  #       difference: fragment("COALESCE(?, 0) - COALESCE(?, 0)", b.actual_this_year, a.acc_bal_in_lcy)
  #     })
  #   |> Repo.all()
  # end

  # MisReports.SourceData.get_gbm_records_for_year_month("202309")
  def get_gbm_records_for_year_month(month) do
    CustSegment
    |> where([a], a.month == ^month and not is_nil(a.mdt_mvmt_in_lcy))
    |> select(
      [a],
      %{
        mdt_mvmt_in_lcy: a.mdt_mvmt_in_lcy,
        year: a.year,
        month: a.month,
        sap_gl_acc_no: a.sap_gl_acc_no,
        sap_gl_acc_name: a.sap_gl_acc_name
      }
    )
    |> Repo.all()
  end

  def get_cust_segment_values(month) do
    CustSegment
    |> where([a], a.month == ^month and not is_nil(a.mdt_mvmt_in_lcy))
    |> select(
      [a],
      %{
        mdt_mvmt_in_lcy: a.mdt_mvmt_in_lcy,
        year: a.year,
        month: a.month,
        sap_gl_acc_no: a.sap_gl_acc_no,
        sap_gl_acc_name: a.sap_gl_acc_name,
        ccy_cat: a.ccy_cat,
        acc_bal_in_lcy: a.acc_bal_in_lcy
      }
    )
    |> Repo.all()
  end

  @doc """
  Gets a single cust_segment.

  Raises `Ecto.NoResultsError` if the Cust segment does not exist.

  ## Examples

      iex> get_cust_segment!(123)
      %CustSegment{}

      iex> get_cust_segment!(456)
      ** (Ecto.NoResultsError)

  """
  def get_cust_segment!(id), do: Repo.get!(CustSegment, id)

  @doc """
  Creates a cust_segment.

  ## Examples

      iex> create_cust_segment(%{field: value})
      {:ok, %CustSegment{}}

      iex> create_cust_segment(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_cust_segment(attrs \\ %{}) do
    try do
      %CustSegment{}
      |> CustSegment.changeset(attrs)
      |> Repo.insert()
    rescue
      e in Ecto.InvalidChangesetError -> {:error, e.changeset}
      e -> {:error, "Creation failed: #{inspect(e)}"}
    end
  end

  def update_cust_segment(%CustSegment{} = cust_segment, attrs) do
    try do
      cust_segment
      |> CustSegment.changeset(attrs)
      |> Repo.update()
    rescue
      e in Ecto.StaleEntryError -> {:error, :stale_data}
      e in Ecto.InvalidChangesetError -> {:error, e.changeset}
      e -> {:error, "Update failed: #{inspect(e)}"}
    end
  end

  def delete_cust_segment(%CustSegment{} = cust_segment) do
    try do
      Repo.delete(cust_segment)
    rescue
      e in Ecto.StaleEntryError -> {:error, :stale_data}
      e -> {:error, "Deletion failed: #{inspect(e)}"}
    end
  end


  @doc """
  Returns an `%Ecto.Changeset{}` for tracking cust_segment changes.

  ## Examples

      iex> change_cust_segment(cust_segment)
      %Ecto.Changeset{data: %CustSegment{}}

  """
  def change_cust_segment(%CustSegment{} = cust_segment, attrs \\ %{}) do
    CustSegment.changeset(cust_segment, attrs)
  end

  def list_cust_segmentations(params) do
    CustSegment
    # |> preload([:checker, :maker, :role])
    |> handle_cust_seg_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  # def get_gut_sap_number(month) do
  #   CustSegment
  #     |> join(:left, [a], b in "tbl_trial_balance_entries", on: a.sap_gl_acc_no == b.gl_no)
  #     |> where([a, b], a.month == ^month)
  #     |> select([a, b], %{
  #       sap_gl_acc_no: a.sap_gl_acc_no,
  #       acc_bal_in_lcy: coalesce(a.acc_bal_in_lcy, 0),
  #       actual_this_year: coalesce(b.actual_this_year, 0),
  #       gl_desc: b.gl_desc,
  #       gl_no: b.gl_no,
  #       difference: fragment("COALESCE(?, 0) - COALESCE(?, 0)", b.actual_this_year, a.acc_bal_in_lcy)
  #     })
  #     |> Repo.all()
  #   end

  def get_gut_sap_number(nil, _params), do: MisReportsWeb.LiveHelpers.empty_scrivener_page()

  def empty_scrivener_page() do
    %Scrivener.Page{
      entries: [],
      page_number: 1,
      page_size: 10,
      total_entries: 0,
      total_pages: 1
    }
  end

  def format_amt(amt) do
    case amt do
      "0.0" ->
        "0.00"

      0.0 ->
        "0.00"

      0 ->
        "0.00"

      nil ->
        "0.00"

      _ ->
        Decimal.abs(amt)
        |> Decimal.div(Decimal.new("1000"))
        |> Decimal.round(0)
        |> Decimal.to_string()
        |> String.replace(~r/(\d)(?=(\d{3})+(?!\d))/, "\\1,")
    end
  end

  def format_amt_no_abs(amt) do
    case amt do
      "0.0" ->
        "0.00"

      0.0 ->
        "0.00"

      0 ->
        "0.00"

      nil ->
        "0.00"

      _ ->
        amt
        |> Decimal.div(Decimal.new("1000"))
        |> Decimal.round(0)
        |> Decimal.to_string()
        |> String.replace(~r/(\d)(?=(\d{3})+(?!\d))/, "\\1,")
    end
  end

  defp handle_cust_seg_filter(query, nil), do: query

  defp handle_cust_seg_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"legal_entity", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.legal_entity, ^value))

      {"country", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.country, ^value))

      {"month", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.month, ^value))

      {"ccy_code", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.ccy_code, ^value))

      {"ccy_cat", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.ccy_cat, ^value))

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp isearch_filter(query, nil), do: query

  defp isearch_filter(query, isearch) do
    search_term = isearch

    query
    |> where([a], fragment("lower(?) like lower(?)", a.country, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.legal_entity, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.ccy_code, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.ccy_cat, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.acc_bal_in_ccy, ^search_term))
  end

  def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"

  # def user_lookup(id) do
  #   fields =
  #     [
  #       role: [:id, :role_desc],
  #       maker: [:id, :first_name, :last_name],
  #       checker: [:id, :first_name, :last_name]
  #     ] ++ User.__schema__(:fields)

  #   User
  #   |> where(id: ^id)
  #   |> preload([:maker, :checker, :role])
  #   |> select([a], map(a, ^fields))
  #   |> Repo.one()
  # end

  alias MisReports.SourceData.AllDeal

  @doc """
  Returns the list of tbl_all_deal.

  ## Examples

      iex> list_tbl_all_deal()
      [%AllDeal{}, ...]

  """
  def list_tbl_all_deal do
    Repo.all(AllDeal)
  end

  @doc """
  Gets a single all_deal.

  Raises `Ecto.NoResultsError` if the All deal does not exist.

  ## Examples

      iex> get_all_deal!(123)
      %AllDeal{}

      iex> get_all_deal!(456)
      ** (Ecto.NoResultsError)

  """
  def get_all_deal!(id), do: Repo.get!(AllDeal, id)

  @doc """
  Creates a all_deal.

  ## Examples

      iex> create_all_deal(%{field: value})
      {:ok, %AllDeal{}}

      iex> create_all_deal(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_all_deal(attrs \\ %{}) do
    %AllDeal{}
    |> AllDeal.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a all_deal.

  ## Examples

      iex> update_all_deal(all_deal, %{field: new_value})
      {:ok, %AllDeal{}}

      iex> update_all_deal(all_deal, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_all_deal(%AllDeal{} = all_deal, attrs) do
    all_deal
    |> AllDeal.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a all_deal.

  ## Examples

      iex> delete_all_deal(all_deal)
      {:ok, %AllDeal{}}

      iex> delete_all_deal(all_deal)
      {:error, %Ecto.Changeset{}}

  """
  def delete_all_deal(%AllDeal{} = all_deal) do
    Repo.delete(all_deal)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking all_deal changes.

  ## Examples

      iex> change_all_deal(all_deal)
      %Ecto.Changeset{data: %AllDeal{}}

  """
  def change_all_deal(%AllDeal{} = all_deal, attrs \\ %{}) do
    AllDeal.changeset(all_deal, attrs)
  end

  def get_all_deal_by_filename(filename) do
    AllDeal
    |> where(src_filename: ^filename)
    |> Repo.all()
  end

  def get_all_deal_by_date(from, to) do
    AllDeal
    |> where(
      [a],
      fragment("EOMONTH(?) >= ?", a.date, ^from) and
      fragment("EOMONTH(?) <= ?", a.date, ^to)
    )
    |> Repo.all()
  end

  def all_deal_list(params) do
    AllDeal
    |> handle_all_deal_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp handle_all_deal_filter(query, nil), do: query

  defp handle_all_deal_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        all_deal_isearch_filter(query, sanitize_term(value))

      {"date", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.date, ^value))

      {"country", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.country, ^value))

      {"trade_id", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.trade_id, ^value))

      {"prod_type", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.prod_type, ^value))

      {"counter_party", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.counter_party, ^value))

      {"trade_ccy", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.trade_ccy, ^value))

      {"issuer", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.issuer, ^value))

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp all_deal_isearch_filter(query, nil), do: query

  defp all_deal_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.trade_id, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.country, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.prod_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.total_tenor, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.trade_ccy, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.counter_party, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.coupon, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.issuer, ^search_term))
  end

  def query_records_from_deals_all_31c(from, to) do
    AllDeal
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.date, ^to) and
        not is_nil(a.clean_setlemt_amt) and a.counter_party_grp != "INTER-DIVISIONAL" and
        a.prod_type in ["CallNotice", "Cash"] and a.b_s == "Loan"
    )
    |> select([c], %{
      product_type: c.prod_type,
      bal_sheet: c.b_s,
      trade_cur: c.trade_ccy,
      counterparty_group: c.counter_party_grp,
      clean_settlement_amt: c.clean_setlemt_amt,
      counter_party: c.counter_party,
      trade_dt: c.trade_dt,
      maturity_dt: c.maturity_dt,
      coupon: c.coupon,
      total_tenor: c.total_tenor,
      trade_id: c.trade_id,
      report_date: c.date
    })
    |> Repo.all()
  end

  def get_gov_bonds_data do
    query =
      from d in AllDeal,
        where: d.prod_type == "Bond",
        select: %{prod_type: d.prod_type, clean_setlemt_base: d.clean_setlemt_base}

    Repo.all(query)
  end

  def get_fcy_amotized_bonds_1c(date) do
    AllDeal
    |> where(
      [a],
      fragment(
        "EOMONTH(?) = ?", a.date, ^date
      ) and
      a.prod_type in ["Bond", "BondMMDiscount"] and
      a.counter_party_grp != "INTER-DIVISIONAL"
    )
    |> select([a], %{
      clean_setlemt_amt: a.clean_setlemt_amt,
      cary_val: a.cary_val,
      mtm: a.mtm,
      trade_ccy: a.trade_ccy,
      accbok: a.accbok,
      prod_type: a.prod_type,
      issuer: a.issuer,
      date: a.date,

    })
    |> Repo.all()
  end

  alias MisReports.SourceData.CustContribution

  @doc """
  Returns the list of tbl_cust_contribution.

  ## Examples

      iex> list_tbl_cust_contribution()
      [%CustContribution{}, ...]

  """
  def list_tbl_cust_contribution do
    Repo.all(CustContribution)
  end

  def cust_contribution_by_month(month, ccy_type) do
    CustContribution
    |> where([c], c.month_period == ^month and c.currency_category == ^ccy_type)
    |> Repo.all()
  end

  # MisReports.SourceData.get_records_for_year_month("2023", "09")
  def get_records_for_year_month(year, month) do
    CustContribution
    |> where([a], a.year_period == ^year and a.month_period == ^month)
    |> select(
      [a],
      %{
        account_name: a.account_name,
        currency_category: a.currency_category,
        bal_sap_ledger_no: a.bal_sap_ledger_no,
        actual_credit_balance: a.actual_credit_balance,
        effective_credit_rate: a.effective_credit_rate,
        account_open_date: a.account_open_date,
        account_maturity_date: a.account_maturity_date,
        account_maturity_date: a.account_maturity_date
      }
    )
    |> Repo.all()
  end

  # def get_records_for_year_month(year, month) do
  #   CustContribution
  #   |> where(
  #     [a],
  #     fragment("YEAR(?) = ? AND MONTH(?) = ?", a.inserted_at, ^year, a.inserted_at, ^month)
  #   )
  #   |> select([a], a)
  #   |> Repo.all()
  # end

  def gov_accs_bals(accs, cat) do
    CustContribution
    |> where(
      [c],
      c.currency_category == ^cat and
        c.account_number in ^accs
      # c.month_period == "5" and
    )
    |> Repo.all()
  end

  def bals_foreign_inst(gls) do
    CustContribution
    |> where([c], c.bal_sap_ledger_no in ^gls)
    |> Repo.all()
  end

  def get_bal_sap_ledger_numbers() do
    ["**********", "**********", "**********"]
  end

  # MisReports.SourceData.test_query_31c("2023", "09")
  def test_query_31c(year, month) do
    gls = get_bal_sap_ledger_numbers()

    CustContribution
    |> where(
      [a],
      a.year_period == ^year and a.month_period == ^month and a.bal_sap_ledger_no in ^gls and
        not is_nil(a.actual_debit_balance)
    )
    |> select([c], %{
      customer_name: c.customer_name,
      currency_category: c.currency_category,
      actual_debit_balance: c.actual_debit_balance,
      account_name: c.account_name
    })
    |> Repo.all()
  end

  # MisReports.SourceData.list_with_institution_types("2023", "09")
  def list_with_institution_types(year, month) do
    gls = get_bal_sap_ledger_numbers()

    CustContribution
    |> where(
      [a],
      a.year_period == ^year and a.month_period == ^month and a.bal_sap_ledger_no in ^gls and
        not is_nil(a.actual_debit_balance)
    )
    |> select([c], %{
      "A" =>
        fragment(
          "CASE WHEN ? LIKE ? OR ? LIKE ? OR ? LIKE ? OR ? LIKE ? THEN 'Bank' ELSE 'Other institution' END AS institution_type",
          c.account_name,
          "%Bank%",
          c.account_name,
          "%ICBC%",
          c.account_name,
          "%Morgan%",
          c.account_name,
          "%Stanbic%"
        ),
      "B" =>
        fragment(
          "CASE WHEN ? = 'FCY' AND ? LIKE ? THEN 'Foreign Banks - parent or related bank'
                    WHEN ? = 'LCY' AND ? LIKE ? AND ? NOT LIKE ? THEN 'Foreign Banks - parent or related bank'
                    WHEN ? = 'LCY' OR ? LIKE ? THEN 'Domestic Bank'
                    WHEN ? = 'FCY' AND NOT (? LIKE ? OR ? LIKE ? OR ? LIKE ? OR ? LIKE ?) THEN 'Foreign Bank Unrelated'
                    ELSE 'Other'
                END AS relationship",
          c.currency_category,
          c.account_name,
          "%Standard%",
          c.currency_category,
          c.account_name,
          "%Standard%",
          c.account_name,
          "%Zambia%",
          c.currency_category,
          c.account_name,
          "%ABSA Bank Ltd%",
          c.currency_category,
          c.account_name,
          "%Stanbic%",
          c.account_name,
          "%Standard%",
          c.account_name,
          "%ICBC%",
          c.account_name,
          "%ABSA%"
        ),
      "C" => " ",
      "E" =>
        fragment(
          "CASE WHEN ? = 'FCY' THEN 'Foreign'
                  WHEN ? = 'LCY' AND ? LIKE ? THEN 'Domestic'
                  ELSE 'Other' END",
          c.currency_category,
          c.currency_category,
          c.account_name,
          "%Standard%"
        ),
      "I" =>
        fragment(
          "CASE WHEN ? IN (?, ?, ?, ?, ?, ?) THEN 'Working Balance(NOSTROS)' ELSE 'For investments' END",
          c.bal_sap_ledger_no,
          "**********",
          "**********",
          "**********",
          "**********",
          "**********",
          "**********"
        ),
      "D" => c.account_name,
      "F" => c.currency_code,
      "G" => c.actual_debit_balance,
      "H" => c.actual_debit_balance,
      "J" => c.effective_credit_rate,
      "K" => c.account_open_date,
      "L" => c.account_maturity_date,
      "M" =>
        fragment(
          "CASE WHEN DATEDIFF(hour, ?, ?) >= 48 THEN 'Short Term Placement' ELSE 'Current Account' END AS further_details",
          c.account_open_date,
          c.account_maturity_date
        )
    })
    |> Repo.all()
  end

  # def list_with_institution_types(year, month) do
  #   CustContribution
  #   |> where([a], a.year_period == ^year and a.month_period == ^month)
  #   |> select([c], %{
  #       "A" => fragment(
  #         "CASE WHEN ? LIKE ? OR ? LIKE ? OR ? LIKE ? OR ? LIKE ? THEN 'Bank' ELSE 'Other institution' END AS institution_type",
  #           c.account_name, "%Bank%", c.account_name, "%ICBC%", c.account_name, "%Morgan%", c.account_name, "%Stanbic%"
  #       ),
  #       "B" => fragment(
  #         "CASE WHEN ? = 'FCY' AND ? LIKE ? THEN 'Foreign Banks - parent or related bank'
  #                 WHEN ? = 'FCY' THEN 'Foreign Banks Unrelated'
  #                 WHEN ? = 'LCY' AND ? LIKE ? THEN 'Foreign Banks - parent or related bank'
  #                 WHEN ? = 'LCY' THEN 'Domestic Bank'
  #                 ELSE 'Other' END AS relationship",
  #         c.currency_category, c.account_name, "%Standard%", c.currency_category, c.currency_category,
  #         c.account_name, "%Standard%", c.currency_category),
  #       "C" => " ",
  #       "E" => fragment("CASE WHEN ? = 'FCY' THEN 'Foreign'
  #                 WHEN ? = 'LCY' AND ? LIKE ? THEN 'Domestic'
  #                 ELSE 'Other' END", c.currency_category, c.currency_category, c.account_name, "%Standard%"),

  #       "I" => fragment(
  #         "CASE WHEN LEFT(?, CHARINDEX(' ', CONCAT(?, ' ')) - 1) IN (?, ?, ?, ?, ?, ?) THEN 'Working Balance(NOSTROS)' ELSE 'For investments' END",
  #         c.balance_sap_ledger, c.balance_sap_ledger,
  #         "**********", "**********", "**********", "**********", "**********", "**********"
  #       ),
  #       "D" => c.account_name,
  #       "F" => c.currency_code,
  #       "G" => c.actual_credit_balance,
  #       "H" => fragment("ROUND((? * 21.0250) / 1000, 0)",  c.actual_credit_balance),
  #       "J" => c.effective_credit_rate,
  #       "K" => c.account_open_date,
  #       "L" => c.account_maturity_date,
  #       "M" => fragment("CASE WHEN DATEDIFF(hour, ?, ?) >= 48 THEN 'Short Term Placement' ELSE 'Current Account' END AS further_details", c.account_open_date, c.account_maturity_date)
  #     })
  #   |>Repo.all()
  # end

  defp get_bal_sap_ledger_no_31d() do
    [
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********"
    ]
  end

  # MisReports.SourceData.list_institution_scd31d("2023", "09")

  def list_institution_scd31d(year, month) do
    ledger_no = get_bal_sap_ledger_no_31d()

    CustContribution
    |> where(
      [c],
      c.year_period == ^year and c.month_period == ^month and c.bal_sap_ledger_no in ^ledger_no and
        not is_nil(c.actual_credit_balance)
    )
    |> select([c], %{
      "A" =>
        fragment(
          "CASE WHEN ? LIKE ? THEN 'Bank' ELSE 'Other Financial Institution' END AS institution_type",
          c.account_name,
          "%Bank%"
        ),
      "B" =>
        fragment(
          "CASE WHEN ? = 'FCY' AND ? LIKE ? AND ? LIKE ? THEN 'Foreign Banks - parent or related bank'
                  WHEN ? NOT LIKE ? THEN ''
                  WHEN ? = 'LCY' AND ? LIKE ? AND ? NOT LIKE ? THEN 'Foreign Banks - parent or related bank'
                  WHEN ? = 'LCY' OR ? LIKE ? THEN 'Domestic Bank'
                  WHEN ? = 'FCY' AND NOT (? LIKE ? OR ? LIKE ? OR ? LIKE ? OR ? LIKE ?) THEN 'Foreign Bank Unrelated'
                  ELSE 'Other'
              END AS relationship",
          c.currency_category,
          c.account_name,
          "%Standard%",
          c.account_name,
          "%Bank%",
          c.account_name,
          "%Bank%",
          c.currency_category,
          c.account_name,
          "%Standard%",
          c.account_name,
          "%Zambia%",
          c.currency_category,
          c.account_name,
          "%ABSA Bank Ltd%",
          c.currency_category,
          c.account_name,
          "%Stanbic%",
          c.account_name,
          "%Standard%",
          c.account_name,
          "%ICBC%",
          c.account_name,
          "%ABSA%"
        ),
      "C" =>
        fragment(
          "CASE WHEN ? LIKE ? THEN ' ' ELSE 'Security firms' END AS institution_type",
          c.account_name,
          "%Bank%"
        ),
      "E" => fragment("CASE WHEN ? = 'FCY' THEN 'Foreign'
                          WHEN ? = 'LCY' THEN 'Domestic'
                          ELSE 'Other' END", c.currency_category, c.currency_category),
      "I" =>
        fragment(
          "CASE
             WHEN YEAR(?) = ? THEN 'loans and advances'
             ELSE 'Others'
           END AS nature_of_amt_owed",
          c.account_maturity_date,
          2099
        ),
      "D" => c.account_name,
      "F" => c.currency_code,
      "G" => c.actual_credit_balance,
      "H" => c.actual_credit_balance,
      "J" => c.effective_credit_rate,
      "K" => c.account_open_date,
      "L" => c.account_maturity_date,
      "M" =>
        fragment(
          "CASE WHEN DATEDIFF(hour, ?, ?) >= 48 THEN 'Current Account' ELSE '' END AS further_details",
          c.account_open_date,
          c.account_maturity_date
        )
    })
    |> Repo.all()
  end

  # "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********"
  defp get_bal_sap_ledger_no_31c() do
    [
      "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********"
    ]
  end

  # "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********"

  # MisReports.SourceData.get_ccr_entries_31c("2025", "01")
  def get_ccr_entries_31c(year, month) do
    ledger_no = get_bal_sap_ledger_no_31c()
    start_date = Date.from_iso8601!("#{year}-#{month}-01")
    end_date = Date.end_of_month(start_date)

    trial_balance_amount = get_trial_balance_amount(end_date)

    main_query =
      from(cc in CustContribution)
      |> join(:left, [cc], ad in "tbl_all_deal", on: cc.account_number == ad.trade_id)
      |> where(
        [cc, ad],
        cc.year_period == ^year and
          cc.month_period == ^month and
          cc.bal_sap_ledger_no in ^ledger_no and
          not is_nil(cc.actual_debit_balance)
      )
      |> select_query(:main)

    results = main_query |> distinct(true) |> Repo.all()

    updated_results = update_specific_record(results, trial_balance_amount)
    final_results = update_debit_balance_with_nominal(updated_results, "********", end_date)

    final_results
  end

  defp select_query(query, :main) do
    select(query, [cc, ad], %{
      account_name: coalesce(cc.account_name, ad.counter_party),
      account_number: coalesce(cc.account_number, ad.trade_id),
      currency_code: coalesce(cc.currency_code, ad.trade_ccy),
      account_open_date: coalesce(cc.account_open_date, ad.trade_dt),
      account_maturity_date: coalesce(cc.account_maturity_date, ad.maturity_dt),
      actual_debit_balance: coalesce(cc.actual_debit_balance, ad.orig_nominal),
      effective_debit_rate: cc.effective_debit_rate,
      balance_sap_ledger: cc.balance_sap_ledger,
      trade_id: ad.trade_id,
      product_type: ad.prod_type
    })
  end

  defp select_query(query, :subquery) do
    select(query, [ad], %{
      account_name: ad.counter_party,
      account_number: ad.trade_id,
      currency_code: ad.trade_ccy,
      account_open_date: ad.trade_dt,
      account_maturity_date: ad.maturity_dt,
      actual_debit_balance: ad.orig_nominal,
      effective_debit_rate: nil,
      balance_sap_ledger: nil,
      trade_id: ad.trade_id,
      product_type: ad.prod_type
    })
  end

  defp get_trial_balance_amount(end_date) do
    result =
      from(tbe in "tbl_trial_balance_entries",
        where: tbe.gl_no == "86850" and tbe.date == ^end_date,
        select: tbe.actual_this_year
      )
      |> Repo.one()

    # IO.inspect(result, label: "Trial Balance Amount for #{end_date}")
    result
  end

  defp get_deals_all_amount(_trade_id, end_date) do
    result =
      from(tbe in "tbl_all_deal",
        where: fragment("EOMONTH(?) = ?", tbe.date, ^end_date) and tbe.trade_id == "********",
        select: tbe.orig_nominal
      )
      |> Repo.one()

    # IO.inspect(result, label: "original nominal Amount for #{end_date}")
    result
  end

  defp update_debit_balance_with_nominal(results, trade_id, end_date) do
    orig_nominal = get_deals_all_amount(trade_id, end_date)

    Enum.map(results, fn record ->
      if record.account_number == trade_id do
        %{record | actual_debit_balance: orig_nominal}
      else
        record
      end
    end)
  end

  defp update_specific_record(results, trial_balance_amount) do
    Enum.map(results, fn record ->
      if record.account_number == "********" do
        %{record | actual_debit_balance: trial_balance_amount}
      else
        record
      end
    end)
  end

  defp ccr_gls() do
    [
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********",
      "**********"
    ]
  end

  def schd31d_query(year, month) do
    gls = ccr_gls()

    CustContribution
    |> where(
      [c],
      c.year_period == ^year and c.month_period == ^month and c.bal_sap_ledger_no in ^gls and
        not is_nil(c.actual_credit_balance)
    )
    |> group_by([c], [
      c.account_name,
      c.bal_sap_ledger_no,
      c.effective_credit_rate,
      c.account_open_date,
      c.account_maturity_date,
      c.currency_category,
      c.currency_code,
      c.actual_credit_balance
    ])
    |> select([c], %{
      actual_credit: sum(coalesce(c.actual_credit_balance, 0.00)),
      account_name: c.account_name,
      deal_date: c.account_open_date,
      maturity_date: c.account_maturity_date,
      cur_cat: c.currency_category,
      cur_code: c.currency_code,
      interest_rate: coalesce(c.effective_credit_rate, 0),
      gl_no: c.bal_sap_ledger_no
    })
    |> Repo.all()
  end

  # MisReports.SourceData.number_depositors_data_18b("2023-09-30")
  def number_depositors_data_18b(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)

    CustContribution
    |> join(:left, [a], b in "tbl_branch", on: a.branch_number == b.number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year)
    |> where(
      [a, b],
      a.product_category_sap in [
        "Savings Deposit Deal",
        "Call Deposit Deal",
        "Fixed Term Deposit Deal",
        "Current Account Facility Deal"
      ] and
        not is_nil(a.account_number) and
        b.status == "A"
    )
    |> order_by([a, b], desc: a.product_category_sap)
    |> select([a, b, c, d, e], %{
      "account_no" => a.account_number,
      "province" => b.province,
      "product_category_sap" => a.product_category_sap,
      "branch_number" => b.number
    })
    |> Repo.all()
  end

  @doc """
  Gets a single cust_contribution.

  Raises `Ecto.NoResultsError` if the Cust contribution does not exist.

  ## Examples

      iex> get_cust_contribution!(123)
      %CustContribution{}

      iex> get_cust_contribution!(456)
      ** (Ecto.NoResultsError)

  """
  def get_cust_contribution!(id), do: Repo.get!(CustContribution, id)

  @doc """
  Creates a cust_contribution.

  ## Examples

      iex> create_cust_contribution(%{field: value})
      {:ok, %CustContribution{}}

      iex> create_cust_contribution(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_cust_contribution(attrs \\ %{}) do
    %CustContribution{}
    |> CustContribution.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a cust_contribution.

  ## Examples

      iex> update_cust_contribution(cust_contribution, %{field: new_value})
      {:ok, %CustContribution{}}

      iex> update_cust_contribution(cust_contribution, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_cust_contribution(%CustContribution{} = cust_contribution, attrs) do
    cust_contribution
    |> CustContribution.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a cust_contribution.
  ## Examples

      iex> delete_cust_contribution(cust_contribution)
      {:ok, %CustContribution{}}

      iex> delete_cust_contribution(cust_contribution)
      {:error, %Ecto.Changeset{}}

  """
  def delete_cust_contribution(%CustContribution{} = cust_contribution) do
    Repo.delete(cust_contribution)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking cust_contribution changes.

  ## Examples

      iex> change_cust_contribution(cust_contribution)
      %Ecto.Changeset{data: %CustContribution{}}

  """
  def change_cust_contribution(%CustContribution{} = cust_contribution, attrs \\ %{}) do
    CustContribution.changeset(cust_contribution, attrs)
  end

  def ccr_by_filename(filename) do
    CustContribution
    |> where(src_filename: ^filename)
    |> Repo.all()
  end

  def get_ccr_by_year_and_month(year, month) do
    CustContribution
    |> where(month_period: ^month, year_period: ^year)
    |> Repo.all()
  end

  def get_ccr_by_year_and_month(year, month, gls) do
    CustContribution
    |> where(
      [a],
      a.month_period == ^month and
        a.year_period == ^year and
        a.bal_sap_ledger_no in ^gls and
        not is_nil(a.actual_credit_balance)
    )
    |> Repo.all()
  end

  def get_ccr_by_bal_sheet(year, month, gls) do
    CustContribution
    |> where(
      [a],
      a.month_period == ^month and
        a.year_period == ^year and
        a.bal_sap_ledger_no in ^gls and
        not is_nil(a.actual_credit_balance)
    )
    |> select([a], %{
      currency: a.currency_code,
      actual_credit_balance: a.actual_credit_balance,
      deposit_type: a.product_category_sap,
      bal_sap_ledger_no: a.bal_sap_ledger_no,
      effective_credit_rate: a.effective_credit_rate,
      account_name: a.account_name
    })
    |> Repo.all()
  end

  def get_all_customer_deposits(year, month, gls) do
    CustContribution
    |> where(
      [a],
      a.month_period == ^month and
        a.year_period == ^year and
        a.bal_sap_ledger_no in ^gls and
        not is_nil(a.actual_credit_balance) and
        a.actual_credit_balance != 0
    )
    |> join(:left, [a], b in "tbl_customer_details",
      on: a.customer_number_local_cif == b.account_no
    )
    |> select([a, b], %{
      currency: a.currency_code,
      cust_type: b.cust_type,
      account_number: a.customer_number_local_cif,
      account_name: a.account_name,
      deposit_type: a.product_category_sap,
      amount: coalesce(a.actual_credit_balance, 0.00),
      economic_sector: b.economic_sector,
      economic_sub_sector: b.economic_sub_sector,
      institutional_units_and_sectors: b.institutional_units_and_sectors,
      account_maturity_date: a.account_maturity_date,
      bal_sap_ledger_no: a.bal_sap_ledger_no,
      account_open_date: a.account_open_date
    })
    |> Repo.all()
  end

  def get_all_cust_deposits_by_group(year, month, gls) do
    CustContribution
    |> where(
      [a],
      a.month_period == ^month and
        a.year_period == ^year and
        a.bal_sap_ledger_no in ^gls and
        not is_nil(a.actual_credit_balance) and
        a.actual_credit_balance != 0
    )
    |> join(:left, [a], b in "tbl_customer_details",
      on: a.customer_number_local_cif == b.account_no
    )
    |> select([a, b], %{
      currency: a.currency_code,
      cust_type: b.cust_type,
      account_number: a.customer_number_local_cif,
      account_name: coalesce(b.group, a.account_name),
      deposit_type: a.product_category_sap,
      amount: coalesce(a.actual_credit_balance, 0.00),
      rate: coalesce(a.effective_credit_rate, 0),
      economic_sector: b.economic_sector,
      economic_sub_sector: b.economic_sub_sector,
      bal_sap_ledger_no: a.bal_sap_ledger_no,
      institutional_units_and_sectors: b.institutional_units_and_sectors,
      account_maturity_date: a.account_maturity_date,
      account_open_date: a.account_open_date
    })
    |> Repo.all()
  end

  def get_acc_not_maintained(year, month, gls) do
    CustContribution
    |> where(
      [a],
      a.month_period == ^month and
        a.year_period == ^year and
        a.bal_sap_ledger_no in ^gls and
        not is_nil(a.actual_credit_balance) and
        a.actual_credit_balance != 0
    )
    |> join(:left, [a], b in "tbl_customer_details",
      on: a.customer_number_local_cif == b.account_no
    )
    |> where(
      [a, b],
      is_nil(b.account_no)
    )
    |> select(
      [a, b],
      %{account_number: a.customer_number_local_cif}
    )
    |> Repo.all()
  end

  def ccr_list(params) do
    CustContribution
    # |> ccr_isearch_filter(params.isearch)
    |> handle_crr_filter(params.isearch)
    |> order_by(desc: :id)
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp handle_crr_filter(query, nil), do: query

  defp handle_crr_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        ccr_isearch_filter(query, sanitize_term(value))

      {"month_period", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.month_period, ^value))

      {"customer_name", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.customer_name, ^value))

      {"account_number", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.account_number, ^value))

      {"year_period", value}, query when byte_size(value) > 0 ->
        value = sanitize_term(value)
        where(query, [a], fragment("lower(?) LIKE lower(?)", a.year_period, ^value))

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp ccr_isearch_filter(query, nil), do: query

  defp ccr_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.month_period, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.customer_name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.year_period, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.bal_sap_ledger_no, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.currency_code, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.actual_credit_balance, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.account_maturity_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.account_open_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.account_number, ^search_term))
  end

  def depositor_sap_gls() do
    [
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********"
    ]
  end

  def get_all_depositors(start_date, start_dt, top_depositors \\ nil) do
    {year_one, month_one, year_two, month_two} = extract_dates(start_date, start_dt)
    %{period_one_rate: fx_rate_1, period_two_rate: fx_rate_2} =
      MisReports.Utilities.get_usd_rates_for_periods(start_date, start_dt)

    top_depositors = top_depositors || 500

    period_one = fetch_period_one_data(year_one, month_one, top_depositors)
    period_one_accounts = MapSet.new(period_one, & &1.account_number)
    period_two = fetch_period_two_data(year_two, month_two, period_one_accounts)

    combine_period_data(period_one, period_two, fx_rate_1, fx_rate_2)
  end

  defp extract_dates(start_date, start_dt) do
    year_one = String.slice(start_date, 0..3)
    month_one = String.slice(start_date, 5..6)
    year_two = String.slice(start_dt, 0..3)
    month_two = String.slice(start_dt, 5..6)

    {year_one, month_one, year_two, month_two}
  end

  defp fetch_period_one_data(year, month, top_depositors) do
    CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^depositor_sap_gls())
    |> where([a, b], not is_nil(b.tax_identification_number))
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate,
      abs_debit_balance: fragment("ABS(?)", a.actual_credit_balance)
    })
    |> distinct(true)
    |> order_by([a], desc: fragment("ABS(?)", a.actual_credit_balance))
    |> limit(^top_depositors)
    |> Repo.all()
  end

  defp fetch_period_two_data(year, month, period_one_accounts) do
    query = CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^depositor_sap_gls())
    |> where([a, b], not is_nil(b.tax_identification_number))
    |> where([a], a.account_number in ^MapSet.to_list(period_one_accounts))
    |> distinct(true)
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate
    })
    |> order_by([a], asc: a.account_number)

    Repo.all(query)
    |> Map.new(&{&1.account_number, &1})
  end

  defp combine_period_data(period_one, period_two_map, fx_rate_1, fx_rate_2) do
    Enum.map(period_one, fn record_one ->
      record_two = Map.get(period_two_map, record_one.account_number, %{
        actual_debit_balance: Decimal.new(0),
        actual_credit_balance: Decimal.new(0)
      })

      {rate_1, rate_2} = determine_fx_rates(record_one.currency_category, fx_rate_1, fx_rate_2)

      build_combined_record(record_one, record_two, rate_1, rate_2)
    end)
  end

  defp determine_fx_rates(currency_category, fx_rate_1, fx_rate_2) do
    case String.downcase(currency_category) do
      "lcy" -> {Decimal.new(1), Decimal.new(1)}
      _ -> {fx_rate_1, fx_rate_2}
    end
  end

  defp build_combined_record(record_one, record_two, rate_1, rate_2) do
    %{
      tax_identification_number: record_one.tax_identification_number,
      account_number: record_one.account_number,
      account_name: record_one.account_name,
      product_code_source: record_one.product_code_source,
      cif: record_one.cif,
      currency_category: record_one.currency_category,
      period_one_debit_balance: record_one.actual_debit_balance,
      period_one_credit_balance: record_one.actual_credit_balance,
      period_two_debit_balance: record_two.actual_debit_balance,
      period_two_credit_balance: record_two.actual_credit_balance,
      movement_debit_balance:
        Decimal.sub(record_one.actual_debit_balance || Decimal.new(0),
                   record_two.actual_debit_balance || Decimal.new(0)),
      movement_credit_balance:
        Decimal.sub(record_one.actual_credit_balance || Decimal.new(0),
                   record_two.actual_credit_balance || Decimal.new(0)),
      effective_debit_rate: record_one.effective_debit_rate,
      effective_credit_rate: record_one.effective_credit_rate,
      fx_rate_1: rate_1,
      fx_rate_2: rate_2
    }
  end

  def loans_adavances_ccr_gls() do
    [
      "0000060225", "0000061450", "0000061950", "0000062150", "0000071150", "0000072650", "0000072750", "0000073050",
      "0000065500", "0000070005", "0000070500", "0000070850", "0000073250", "0000073850", "0000076905", "0000076920",
      "0000080500", "0000085255", "0*********", "**********", "**********", "**********", "**********"
    ]
  end

  def get_all_loans_advances(start_date, start_dt, top_borrowers) do
    {year_one, month_one, year_two, month_two} = extract_dates(start_date, start_dt)
    %{period_one_rate: fx_rate_1, period_two_rate: fx_rate_2} =
      MisReports.Utilities.get_usd_rates_for_periods(start_date, start_dt)

    top_borrowers = top_borrowers || 500

    period_one = fetch_loans_period_one_data(year_one, month_one, top_borrowers)
    period_one_accounts = MapSet.new(period_one, & &1.account_number)
    period_two = fetch_loans_period_two_data(year_two, month_two, period_one_accounts)

    combine_loans_period_data(period_one, period_two, fx_rate_1, fx_rate_2)
  end

  defp fetch_loans_period_one_data(year, month, top_borrowers) do
    CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^loans_adavances_ccr_gls())
    |> where([a, b], not is_nil(b.tax_identification_number))
    |> where([a], not is_nil(a.actual_debit_balance))
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate,
      abs_debit_balance: fragment("ABS(?)", a.actual_debit_balance)
    })
    |> distinct(true)
    |> order_by([a], desc: fragment("ABS(?)", a.actual_debit_balance))
    |> limit(^top_borrowers)
    |> Repo.all()
  end

  defp fetch_loans_period_two_data(year, month, period_one_accounts) do
    query = CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^loans_adavances_ccr_gls())
    |> where([a, b], not is_nil(b.tax_identification_number))
    |> where([a], not is_nil(a.actual_debit_balance))
    |> where([a], a.account_number in ^MapSet.to_list(period_one_accounts))
    |> distinct(true)
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate
    })
    |> order_by([a], asc: a.account_number)

    Repo.all(query)
    |> Map.new(&{&1.account_number, &1})
  end

  defp combine_loans_period_data(period_one, period_two_map, fx_rate_1, fx_rate_2) do
    Enum.map(period_one, fn record_one ->
      record_two = Map.get(period_two_map, record_one.account_number, %{
        actual_debit_balance: Decimal.new(0),
        actual_credit_balance: Decimal.new(0)
      })

      {rate_1, rate_2} = determine_fx_rates(record_one.currency_category, fx_rate_1, fx_rate_2)

      build_loans_combined_record(record_one, record_two, rate_1, rate_2)
    end)
  end

  defp build_loans_combined_record(record_one, record_two, rate_1, rate_2) do
    %{
      tax_identification_number: record_one.tax_identification_number,
      account_number: record_one.account_number,
      account_name: record_one.account_name,
      product_code_source: record_one.product_code_source,
      cif: record_one.cif,
      currency_category: record_one.currency_category,
      period_one_debit_balance: record_one.actual_debit_balance,
      period_one_credit_balance: record_one.actual_credit_balance,
      period_two_debit_balance: record_two.actual_debit_balance,
      period_two_credit_balance: record_two.actual_credit_balance,
      movement_debit_balance:
        Decimal.sub(record_one.actual_debit_balance || Decimal.new(0),
                   record_two.actual_debit_balance || Decimal.new(0)),
      movement_credit_balance:
        Decimal.sub(record_one.actual_credit_balance || Decimal.new(0),
                   record_two.actual_credit_balance || Decimal.new(0)),
      effective_debit_rate: record_one.effective_debit_rate,
      effective_credit_rate: record_one.effective_credit_rate,
      fx_rate_1: rate_1,
      fx_rate_2: rate_2
    }
  end

  def loans_to_banks_ccr_gls() do
    [
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********"
    ]
  end

  def get_all_loans_to_banks(start_date, start_dt, top_balances) do
    {year_one, month_one, year_two, month_two} = extract_dates(start_date, start_dt)
    %{period_one_rate: fx_rate_1, period_two_rate: fx_rate_2} =
      MisReports.Utilities.get_usd_rates_for_periods(start_date, start_dt)

      top_balances = top_balances || 500

    period_one = fetch_loans_to_banks_period_one(year_one, month_one, top_balances)
    period_one_accounts = MapSet.new(period_one, & &1.account_number)
    period_two = fetch_loans_to_loans_period_two(year_two, month_two, period_one_accounts)

    combine_loans_period_data(period_one, period_two, fx_rate_1, fx_rate_2)
  end

  defp fetch_loans_to_banks_period_one(year, month, top_balances) do
    CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^loans_to_banks_ccr_gls())
    # |> where([a, b], not is_nil(b.tax_identification_number))
    |> where([a], not is_nil(a.actual_debit_balance))
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate,
      abs_debit_balance: fragment("ABS(?)", a.actual_debit_balance)
    })
    |> distinct(true)
    |> order_by([a], desc: fragment("ABS(?)", a.actual_debit_balance))
    |> limit(^top_balances)
    |> Repo.all()
  end

  defp fetch_loans_to_loans_period_two(year, month, period_one_accounts) do
    query = CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^loans_to_banks_ccr_gls())
    # |> where([a, b], not is_nil(b.tax_identification_number))
    |> where([a], not is_nil(a.actual_debit_balance))
    |> where([a], a.account_number in ^MapSet.to_list(period_one_accounts))
    |> distinct(true)
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate
    })
    |> order_by([a], asc: a.account_number)

    Repo.all(query)
    |> Map.new(&{&1.account_number, &1})
  end

  def balances_due_ccr_gls() do
    [
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********", "**********", "**********", "**********", "**********", "**********",
      "**********", "**********"
    ]
  end

  def get_balances_due_to_banks(start_date, start_dt, top_balances) do
    {year_one, month_one, year_two, month_two} = extract_dates(start_date, start_dt)
    %{period_one_rate: fx_rate_1, period_two_rate: fx_rate_2} =
      MisReports.Utilities.get_usd_rates_for_periods(start_date, start_dt)

      top_balances = top_balances || 500

    period_one = fetch_loans_to_banks_period_one(year_one, month_one, top_balances)
    period_one_accounts = MapSet.new(period_one, & &1.account_number)
    period_two = fetch_loans_to_loans_period_two(year_two, month_two, period_one_accounts)

    combine_loans_period_data(period_one, period_two, fx_rate_1, fx_rate_2)
  end

  defp fetch_loans_to_banks_period_one(year, month, top_balances) do
    CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^balances_due_ccr_gls())
    # |> where([a, b], not is_nil(b.tax_identification_number))
    |> where([a], not is_nil(a.actual_debit_balance))
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate,
      abs_debit_balance: fragment("ABS(?)", a.actual_debit_balance)
    })
    |> distinct(true)
    |> order_by([a], desc: fragment("ABS(?)", a.actual_debit_balance))
    |> limit(^top_balances)
    |> Repo.all()
  end

  defp fetch_loans_to_loans_period_two(year, month, period_one_accounts) do
    query = CustContribution
    |> join(:left, [a], b in "gmo_tpins", on: a.customer_number_local_cif == b.customer_number)
    |> where([a, b], a.month_period == ^month and a.year_period == ^year and a.bal_sap_ledger_no in ^balances_due_ccr_gls())
    # |> where([a, b], not is_nil(b.tax_identification_number))
    |> where([a], not is_nil(a.actual_debit_balance))
    |> where([a], a.account_number in ^MapSet.to_list(period_one_accounts))
    |> distinct(true)
    |> select([a, b], %{
      tax_identification_number: b.tax_identification_number,
      account_number: a.account_number,
      account_name: a.account_name,
      product_code_source: a.product_code_source,
      cif: a.customer_number_local_cif,
      currency_category: a.currency_category,
      actual_debit_balance: a.actual_debit_balance,
      actual_credit_balance: a.actual_credit_balance,
      effective_debit_rate: a.effective_debit_rate,
      effective_credit_rate: a.effective_credit_rate
    })
    |> order_by([a], asc: a.account_number)

    Repo.all(query)
    |> Map.new(&{&1.account_number, &1})
  end


  alias MisReports.SourceData.TrialBalance

  @doc """
  Returns the list of tbl_trial_balance.

  ## Examples

      iex> list_tbl_trial_balance()
      [%TrialBalance{}, ...]

  """
  def list_tbl_trial_balance do
    Repo.all(TrialBalance)
  end

  @doc """
  Gets a single trial_balance.

  Raises `Ecto.NoResultsError` if the Trial balance does not exist.

  ## Examples

      iex> get_trial_balance!(123)
      %TrialBalance{}

      iex> get_trial_balance!(456)
      ** (Ecto.NoResultsError)

  """
  def get_trial_balance!(id), do: Repo.get!(TrialBalance, id)

  def get_gut_sap_number(date, params) do
    # Default sorting and page size if not present
    sort_by = params.sort_by || {:asc, :sap_gl_acc_no}
    page = params.page || 1
    page_size = params.page_size || 10
    month = String.replace(date, "-", "") |> String.slice(0..5)

    query =
      TrialBalance
      |> join(:left, [a], b in "tbl_cust_segmentation", on: a.gl_no == b.sap_gl_acc_no)
      # |> where([a, b], a.date == ^date)
      |> where(
        [a, b],
        a.date == ^date and b.month == ^month and a.gl_no not in ^income_statement_gls()
      )
      |> group_by([a, b], [a.gl_no, a.gl_desc, a.actual_this_year, a.id])
      |> select([a, b], %{
        sap_gl_acc_no: a.gl_no,
        # GUT
        acc_bal_in_lcy: coalesce(sum(b.acc_bal_in_lcy), 0.0),
        # SAP
        actual_this_year: coalesce(a.actual_this_year, 0),
        gl_desc: a.gl_desc,
        difference:
          fragment(
            "abs(COALESCE(?, 0)) - abs(COALESCE(?, 0))",
            coalesce(a.actual_this_year, 0),
            sum(b.acc_bal_in_lcy)
          )
      })
      |> get_gut_sap_isearch_filter(params.isearch)
      |> order_by(^[sort_by])

    result = Repo.paginate(query, page: page, page_size: page_size)

    formatted_result =
      Enum.map(result.entries, fn entry ->
        %{
          sap_gl_acc_no: entry.sap_gl_acc_no,
          acc_bal_in_lcy: format_amt(entry.acc_bal_in_lcy),
          actual_this_year: format_amt(entry.actual_this_year),
          gl_desc: entry.gl_desc,
          difference: format_amt_no_abs(entry.difference)
        }
      end)

    %{result | entries: formatted_result}
  end

  def get_gut_sap_income_statement(nil, _params),
    do: MisReportsWeb.LiveHelpers.empty_scrivener_page()

  def get_gut_sap_income_statement(date, params) do
    sort_by = params.sort_by || {:asc, :sap_gl_acc_no}
    page = params.page || 1
    page_size = params.page_size || 10
    month = String.replace(date, "-", "") |> String.slice(0..5)

    cur_date = Date.from_iso8601!(date) |> Timex.end_of_month()
    prev_date = Timex.shift(cur_date, months: -1) |> Timex.end_of_month()

    income_statement_gls = income_statement_gls()

    # Query current entries with a LEFT JOIN and calculate sum of acc_bal_in_lcy
    current_entries =
      TrialBalance
      |> join(:left, [a], b in "tbl_cust_segmentation", on: a.gl_no == b.sap_gl_acc_no)
      |> where(
        [a, b],
        a.date == ^cur_date and (is_nil(b.month) or b.month == ^month) and
          a.gl_no in ^income_statement_gls() and not is_nil(a.actual_this_year)
      )
      |> group_by([a, b], [a.gl_no, a.gl_desc, a.actual_this_year, a.date, a.id])
      |> select([a, b], %{
        gl_no: a.gl_no,
        gl_desc: a.gl_desc,
        actual_this_year: a.actual_this_year,
        # Sum of acc_bal_in_lcy
        mdt_mvmt_in_lcy: coalesce(sum(b.mdt_mvmt_in_lcy), 0.0),
        date: a.date
      })
      |> get_gut_income_statement_isearch_filter(params.isearch)
      |> order_by(^[sort_by])

    results = Repo.paginate(current_entries, page: page, page_size: page_size)

    # Query previous entries
    previous_entries =
      case Timex.month_name(cur_date.month) do
        "January" ->
          []

        _ ->
          TrialBalance
          |> where(
            [a],
            a.date == ^prev_date and a.gl_no in ^income_statement_gls and
              not is_nil(a.actual_this_year)
          )
          |> select([a], %{
            gl_no: a.gl_no,
            actual_this_year: a.actual_this_year,
            date: a.date
          })
          |> Repo.all()
      end

    # Create a map for the previous entries using gl_no as the key
    previous_entries_map = Map.new(previous_entries, fn entry -> {entry.gl_no, entry} end)

    # Calculate the final result
    formatted_result =
      Enum.map(results.entries, fn current_entry ->
        previous_entry = Map.get(previous_entries_map, current_entry.gl_no, %{})
        prev_actual_this_year = Map.get(previous_entry, :actual_this_year, Decimal.new(0))
        current_actual_this_year = Map.get(current_entry, :actual_this_year, Decimal.new(0))

        actual_this_year_difference = Decimal.sub(current_actual_this_year, prev_actual_this_year)

        actual_this_year_difference_abs =
          Decimal.sub(current_actual_this_year, prev_actual_this_year) |> Decimal.abs()

        # Sum acc_bal_in_lcy (already summed in query)
        mdt_mvmt_in_lcy_sum = Decimal.abs(current_entry.mdt_mvmt_in_lcy)
        mdt_mvmt_in_lcy_sum_to_display = current_entry.mdt_mvmt_in_lcy

        final_difference = Decimal.sub(actual_this_year_difference_abs, mdt_mvmt_in_lcy_sum)

        %{
          gl_no: current_entry.gl_no,
          gl_desc: current_entry.gl_desc,
          # SAP
          actual_this_year_difference: format_amt(actual_this_year_difference),
          # GUT
          mdt_mvmt_in_lcy: format_amt(mdt_mvmt_in_lcy_sum_to_display),
          # GUT - SAP
          final_difference: format_amt_no_abs(final_difference)
        }
      end)

    # Return the results with formatted entries
    %{results | entries: formatted_result}
  end

  defp get_gut_income_statement_isearch_filter(query, nil), do: query
  defp get_gut_income_statement_isearch_filter(query, ""), do: query

  defp get_gut_income_statement_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.gl_no, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.gl_desc, ^search_term))
  end

  defp get_gut_sap_isearch_filter(query, nil), do: query
  defp get_gut_sap_isearch_filter(query, ""), do: query

  defp get_gut_sap_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.gl_no, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.gl_desc, ^search_term))
  end

  @doc """
  Creates a trial_balance.

  ## Examples

      iex> create_trial_balance(%{field: value})
      {:ok, %TrialBalance{}}

      iex> create_trial_balance(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_trial_balance(attrs \\ %{}) do
    %TrialBalance{}
    |> TrialBalance.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a trial_balance.

  ## Examples

      iex> update_trial_balance(trial_balance, %{field: new_value})
      {:ok, %TrialBalance{}}

      iex> update_trial_balance(trial_balance, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_trial_balance(%TrialBalance{} = trial_balance, attrs) do
    trial_balance
    |> TrialBalance.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a trial_balance.

  ## Examples

      iex> delete_trial_balance(trial_balance)
      {:ok, %TrialBalance{}}

      iex> delete_trial_balance(trial_balance)
      {:error, %Ecto.Changeset{}}

  """
  def delete_trial_balance(%TrialBalance{} = trial_balance) do
    Repo.delete(trial_balance)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking trial_balance changes.

  ## Examples

      iex> change_trial_balance(trial_balance)
      %Ecto.Changeset{data: %TrialBalance{}}

  """
  def change_trial_balance(%TrialBalance{} = trial_balance, attrs \\ %{}) do
    TrialBalance.changeset(trial_balance, attrs)
  end

  def get_trial_balance_by_date(from, to) do
    TrialBalance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    |> select([a], %{
      actual_this_year: coalesce(a.actual_this_year, 0.00),
      gl_no: a.gl_no
    })
    |> Repo.all()
  end

  def get_sap_data_entries(from, to) do
    TrialBalance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    |> select([a], %{
      actual_this_year: coalesce(a.actual_this_year, 0.00),
      actual_last_year: coalesce(a.actual_last_year, 0.00),
      gl_desc: a.gl_desc,
      gl_no: a.gl_no,
      date: a.date,
      variance: a.variance
    })
    |> Repo.all()
  end

  def shd1c_gls() do
    [
      "13100", "41200", "41240", "40165", "40045", "41860", "40680", "59200", "40728",
      "41480", "57605", "40030", "100685", "131500", "M105371", "M105140", "M105174",
      "M105323", "M105321", "M101234", "M105126", "M101233", "M105376", "M105129"
    ]
  end

  def get_trial_balance_by_date_1c(date) do
    gl_no = shd1c_gls()

    TrialBalance
    |> where([a], a.date == ^date and a.gl_no in ^gl_no and not is_nil(a.actual_this_year))
    |> select([a], %{
      actual_this_year: coalesce(a.actual_this_year, 0.00),
      gl_no: a.gl_no
    })
    |> Repo.all()
  end

  def get_trial_balance_by_date_shd13(date) do
    date = Date.from_iso8601!(date)

    previous_date = Timex.shift(date, months: -1) |> Timex.end_of_month()

    query =
      from tb in TrialBalance,
        where:
          tb.gl_no in ["M200510", "214100", "210744", "210750", "210766",
            "210767", "305055", "210600"
          ],
        where: fragment("? = ? OR ? = ?", tb.date, ^date, tb.date, ^previous_date),
        order_by: [desc: :date],
        select: %{
          gl_no: tb.gl_no,
          date: tb.date,
          gl_desc: tb.gl_desc,
          previous_date: ^previous_date,
          cur_actual_this_year:
            fragment("CASE WHEN ? = ? THEN ? ELSE NULL END", tb.date, ^date, tb.actual_this_year),
          previous_actual_this_year:
            fragment(
              "CASE WHEN ? = ? THEN ? ELSE NULL END",
              tb.date,
              ^previous_date,
              tb.actual_this_year
            )
        }

    results = Repo.all(query)

    grouped_results =
      Enum.group_by(results, & &1.gl_no)
      |> Enum.map(fn {_gl_no, entries} ->
        %{
          gl_no: hd(entries).gl_no,
          gl_desc: hd(entries).gl_desc,
          date: hd(entries).date,
          previous_date: hd(entries).previous_date,
          cur_actual_this_year: Enum.find_value(entries, & &1.cur_actual_this_year),
          previous_actual_this_year: Enum.find_value(entries, & &1.previous_actual_this_year)
        }
      end)

    grouped_results
  end

  # "214100", "210744", "210750", "210766", "210767"

  # MisReports.SourceData.get_sap_gl_by_date("2023-09-30", "2023-09-30", ["86850"])
  def get_sap_gl_by_date(from, to, gls) do
    TrialBalance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.date, ^to) and
        a.gl_no in ^gls and
        not is_nil(a.actual_this_year)
    )
    |> select([a], %{
      sap_amount: coalesce(a.actual_this_year, 0.00)
    })
    |> Repo.all()
  end

  def get_trial_bal_entries(src_file_id) do
    TrialBalance
    |> where(src_file_id: ^src_file_id)
    |> Repo.all()
  end

  # MisReports.SourceData.get_tb_values("2023-09-30")
  def get_tb_values(date) do
    date = Date.from_iso8601!(date)
    previous_date = Timex.shift(date, months: -1) |> Timex.end_of_month()

    query =
      from tb in TrialBalance,
        where: tb.gl_no in ["M235330", "M235410", "M346000"],
        where: fragment("? = ? OR ? = ?", tb.date, ^date, tb.date, ^previous_date),
        order_by: [desc: :date],
        select: %{
          gl_no: tb.gl_no,
          date: tb.date,
          gl_desc: tb.gl_desc,
          previous_date: ^previous_date,
          cur_actual_this_year:
            fragment("CASE WHEN ? = ? THEN ? ELSE NULL END", tb.date, ^date, tb.actual_this_year),
          previous_actual_this_year:
            fragment(
              "CASE WHEN ? = ? THEN ? ELSE NULL END",
              tb.date,
              ^previous_date,
              tb.actual_this_year
            )
        }

    results = Repo.all(query)

    grouped_results =
      results
      |> Enum.group_by(& &1.gl_no)
      |> Enum.map(fn {_gl_no, entries} ->
        cur_entry = Enum.find(entries, fn e -> e.date == date end)
        prev_entry = Enum.find(entries, fn e -> e.date == previous_date end)

        %{
          gl_no: hd(entries).gl_no,
          gl_desc: hd(entries).gl_desc,
          date: date,
          previous_date: previous_date,
          cur_actual_this_year: if(cur_entry, do: cur_entry.cur_actual_this_year, else: nil),
          previous_actual_this_year:
            if(prev_entry, do: prev_entry.previous_actual_this_year, else: nil)
        }
      end)

    grouped_results
  end

  # MisReports.SourceData.get_ytd_income_statement("2024-01-31")
  def get_ytd_income_statement(date) do
    _year = String.slice(date, 0..3)
    _month = String.slice(date, 5..6)

    cur_date = Date.from_iso8601!(date) |> Timex.end_of_month()
    prev_date = Timex.shift(cur_date, months: -1) |> Timex.end_of_month()

    current_entries =
      TrialBalance
      |> where([a], a.date == ^cur_date and not is_nil(a.actual_this_year))
      |> select([a], %{
        gl_no: a.gl_no,
        actual_this_year: a.actual_this_year,
        date: a.date
      })
      |> Repo.all()

    previous_entries =
      case Timex.month_name(cur_date.month) do
        "January" ->
          []

        _ ->
          TrialBalance
          |> where([a], a.date == ^prev_date and not is_nil(a.actual_this_year))
          |> select([a], %{
            gl_no: a.gl_no,
            actual_this_year: a.actual_this_year,
            date: a.date
          })
          |> Repo.all()
      end

    # Create a map for the previous entries using gl_no as the key
    previous_entries_map = Map.new(previous_entries, fn entry -> {entry.gl_no, entry} end)

    Enum.map(current_entries, fn current_entry ->
      previous_entry = Map.get(previous_entries_map, current_entry.gl_no, %{})
      prev_actual_this_year = Map.get(previous_entry, :actual_this_year, Decimal.new(0))
      current_actual_this_year = Map.get(current_entry, :actual_this_year, Decimal.new(0))

      actual_this_year_difference = Decimal.sub(current_actual_this_year, prev_actual_this_year)

      %{
        gl_no: current_entry.gl_no,
        actual_this_year: current_actual_this_year,
        prev_actual_this_year: prev_actual_this_year,
        actual_this_year_difference: actual_this_year_difference
      }
    end)
  end

  def get_fraud_data(date) do
    TrialBalance
    |> where([a], a.date == ^date and a.gl_no in ["485200"])
    |> select([a], %{
      gl_no: coalesce(a.gl_no, 0),
      actual_this_year: coalesce(a.actual_this_year, 0),
      date: a.date
    })
    |> Repo.all()
  end

  def get_trial_balance_by_date_sh15(date) do
    TrialBalance
    |> where(
      [a],
      fragment("CAST(? AS DATE) = ?", a.date, ^date)
    )
    |> Repo.all()
  end

  def get_tb_values_4d(date) do
    date = Date.from_iso8601!(date)
    previous_date = Timex.shift(date, months: -1) |> Timex.end_of_month()

    TrialBalance
    |> where([a], fragment("? = ? OR ? = ?", a.date, ^date, a.date, ^previous_date))
    |> select([a], %{
      "gl_no" => a.gl_no,
      "actual_this_year" => a.actual_this_year,
      "date" => a.date
    })
    |> Repo.all()
  end

  def get_shd4d_values(date) do
    l24_value = ["62000", "59500", "59510", "59520", "210766", "311500", "311510", "311520"]
    l25_value = ["M114200", "M105420", "210767", "311530", "311540", "311560"]

    l24_sum =
      TrialBalance
      |> where([a], a.date == ^date and a.gl_no in ^l24_value and not is_nil(a.actual_this_year))
      |> select([a], sum(coalesce(a.actual_this_year, 0.00)))
      |> Repo.one()

    l25_sum =
      TrialBalance
      |> where([a], a.date == ^date and a.gl_no in ^l25_value and not is_nil(a.actual_this_year))
      |> select([a], sum(coalesce(a.actual_this_year, 0.00)))
      |> Repo.one()

    %{
      "L24" => l24_sum,
      "L25" => l25_sum
    }
  end

  def get_gls_not_maintained(date) do
    TrialBalance
    |> where(
      [a],
      a.date == ^date
    )
    |> join(:left, [a], b in "bank_accounts", on: a.gl_no == b.acc_no)
    |> where(
      [a, b],
      is_nil(b.acc_no)
    )
    |> select(
      [a, b],
      %{
        gl_no: a.gl_no,
        gl_desc: a.gl_desc
      }
    )
    |> Repo.all()
  end

  def gl_list() do
    [
      "401500", "404500", "401900", "404300", "404302", "406150", "407340", "407320", "407341",
      "492780", "495134", "497725", "497735", "497170", "401901", "403000", "403300", "403410",
      "401000", "403414", "401110", "403415", "407780", "407840", "407845", "401111", "403004",
      "408000", "404100", "403101", "403100", "406100", "402300", "400000", "405104", "402800",
      "402810", "402812", "402819", "400017", "404800", "404900", "405100", "407100", "493340",
      "402802", "407680", "495710", "400035", "490030", "455900", "490740", "470005", "492160",
      "492260", "428900", "428800", "422300", "424929", "428200", "425700", "425620", "422120",
      "425600", "422130", "422110", "422100", "423400", "427600", "427100", "425400", "428400",
      "421900", "421800", "492280", "423600", "492940", "490830", "425110", "425000", "423900",
      "421400", "421300", "421200", "420300", "420200", "420100", "420000", "490016", "490940",
      "456600", "490760", "492020", "457000", "429800", "491044", "492740", "492800", "492820",
      "440766", "440755", "440753", "440752", "440751", "440750", "441000", "440920", "440880",
      "440860", "440620", "440440", "440780", "440700", "441240", "440800", "440640", "440320",
      "440220", "441200", "440180", "441640", "491820", "490193", "491022", "491020", "492840",
      "493280", "493260", "493220", "493200", "451500", "451600", "451800", "450700", "450500",
      "404200", "491330", "493100", "455100", "460000", "460100", "460700", "460900", "461000",
      "465400", "465900", "466200", "465500", "466300", "466350", "466400", "491850", "462200",
      "475700", "475300", "476200", "476300", "477200", "490220", "490280", "499020", "499260",
      "480000", "480100", "480400", "481300", "481500", "481600", "481900", "482102", "492960",
      "486300", "485000", "486000", "486200", "486600", "486700", "487000", "487400", "491960",
      "466100", "490780", "490880", "492020", "492180", "492320", "493160", "494300", "493780",
      "491250", "492892", "492896", "496957", "496959", "461500", "461501", "461502", "461503",
      "461511", "461512", "496891", "496981", "499291", "499292", "499293", "499294", "499295",
      "499297", "499298", "499299", "499300", "499302", "499304", "499308", "496866", "490218",
      "499060", "492440", "493460", "461510", "499296",
    ]
  end

  @doc """
  Retrieves and processes cost analysis data for two specified periods.

  ## Parameters

  - `start_date` (String): The start date for the first period in the format "YYYY-MM-DD".
  - `end_date` (String): The end date for the first period in the format "YYYY-MM-DD".
  - `start_dt` (String): The start date for the second period in the format "YYYY-MM-DD".
  - `end_dt` (String): The end date for the second period in the format "YYYY-MM-DD".

  ## Returns

  - A map containing the following keys:
    - `period_one` (Map): A map of GL numbers to their accumulated `actual_this_year` values for the first period.
    - `period_two` (Map): A map of GL numbers to their accumulated `actual_this_year` values for the second period.
    - `movement` (Map): A map of GL numbers to the difference in `actual_this_year` values between the two periods.

  ## Example

      iex> MisReports.SourceData.cost_analysis_data("2024-01-01", "2024-01-31", "2025-01-01", "2025-01-31")
      %{
        period_one: %{
          "499020" => #Decimal<1000.00>,
          "499060" => #Decimal<2000.00>,
          "499260" => #Decimal<3000.00>
        },
        period_two: %{
          "499020" => #Decimal<1500.00>,
          "499060" => #Decimal<2500.00>,
          "499260" => #Decimal<3500.00>
        },
        movement: %{
          "499020" => #Decimal<500.00>,
          "499060" => #Decimal<500.00>,
          "499260" => #Decimal<500.00>
        }
      }

  """

  def cost_analysis_data(start_date, end_date, start_dt, end_dt) do
    gl_numbers = gl_list()

    period_one_entries =
      if Timex.diff(Date.from_iso8601!(end_date), Date.from_iso8601!(start_date), :months) == 0 do
        calculate_difference_for_one_month(start_date, end_date, gl_numbers)
      else
        TrialBalance
        |> where([a], a.date >= ^start_date and a.date <= ^end_date and a.gl_no in ^gl_numbers and not is_nil(a.actual_this_year))
        |> select([a], %{
          gl_no: a.gl_no,
          gl_desc: a.gl_desc,
          actual_this_year: a.actual_this_year,
          date: a.date
        })
        |> Repo.all()
      end

    period_two_entries =
      if Timex.diff(Date.from_iso8601!(end_dt), Date.from_iso8601!(start_dt), :months) == 0 do
        calculate_difference_for_one_month(start_dt, end_dt, gl_numbers)
      else
        TrialBalance
        |> where([a], a.date >= ^start_dt and a.date <= ^end_dt and a.gl_no in ^gl_numbers and not is_nil(a.actual_this_year))
        |> select([a], %{
          gl_no: a.gl_no,
          gl_desc: a.gl_desc,
          actual_this_year: a.actual_this_year,
          date: a.date
        })
        |> Repo.all()
      end

    period_one = accumulate_entries(period_one_entries)
    period_two = accumulate_entries(period_two_entries)
    movement = calculate_movement(period_one, period_two)

    %{
      period_one: period_one,
      period_two: period_two,
      movement: movement
    }
  end

  defp calculate_difference_for_one_month(_start_date, end_date, gl_numbers) do
    end_date = Date.from_iso8601!(end_date)
    prev_end_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()

    current_entries =
      TrialBalance
      |> where([a], a.date == ^end_date and a.gl_no in ^gl_numbers and not is_nil(a.actual_this_year))
      |> select([a], %{
        gl_no: a.gl_no,
        gl_desc: a.gl_desc,
        actual_this_year: a.actual_this_year,
        date: a.date
      })
      |> Repo.all()

    previous_entries =
      case Timex.month_name(end_date.month) do
        "January" -> []
        _ ->
          TrialBalance
          |> where([a], a.date == ^prev_end_date and a.gl_no in ^gl_numbers and not is_nil(a.actual_this_year))
          |> select([a], %{
            gl_no: a.gl_no,
            gl_desc: a.gl_desc,
            actual_this_year: a.actual_this_year,
            date: a.date
          })
          |> Repo.all()
      end

    previous_entries_map = Map.new(previous_entries, fn entry -> {entry.gl_no, entry.actual_this_year} end)

    Enum.map(current_entries, fn entry ->
      prev_actual_this_year = Map.get(previous_entries_map, entry.gl_no, Decimal.new(0))
      updated_actual_this_year = Decimal.sub(entry.actual_this_year, prev_actual_this_year)

      %{
        gl_no: entry.gl_no,
        gl_desc: entry.gl_desc,
        actual_this_year: updated_actual_this_year,
        date: entry.date
      }
    end)
  end

  defp accumulate_entries(entries) do
    Enum.reduce(entries, %{}, fn entry, acc ->
      # Append gl_desc to gl_no in the key
      key = "#{entry.gl_no} - #{entry.gl_desc}"
      Map.update(acc, key, entry.actual_this_year, &Decimal.add(&1, entry.actual_this_year))
    end)
  end

  defp calculate_movement(period_one, period_two) do
    keys = Map.keys(period_one) ++ Map.keys(period_two) |> Enum.uniq()

    Enum.reduce(keys, %{}, fn key, acc ->
      value_one = Map.get(period_one, key, Decimal.new(0))
      value_two = Map.get(period_two, key, Decimal.new(0))
      movement = Decimal.sub(value_one, value_two)
      Map.put(acc, key, movement)
    end)
  end

  def interest_split_gls() do
    [
      "700085", "700220", "700115", "705900", "708600", "708605", "708620", "709500", "700095", "700272", "700273",
      "703905", "706400", "702300", "735700", "708500", "709800", "708705", "870016", "734260", "702000", "728800",
      "734020", "734100", "723200", "700005", "728900", "727500", "734040", "725300", "733760", "733500", "700130",
      "700132", "703200", "706100", "721000", "754800", "742200", "763080", "763105", "734800", "749000", "740115",
      "744800", "734840", "734720", "734960", "744200", "762900", "741303", "734585", "757310", "757320", "757330",
      "757340", "763460"
    ]
  end

  def get_filtered_accounts(start_dt, end_dt) do
    interest_gls = interest_split_gls()
    start_dt = Date.from_iso8601!(start_dt)
    end_dt = Date.from_iso8601!(end_dt)

    # Extract the month in 'YYYYMM' format from start_dt
    month = "#{start_dt.year}#{String.pad_leading(Integer.to_string(start_dt.month), 2, "0")}"

    # Calculate the previous month's end date
    prev_month_end_dt = Timex.shift(start_dt, months: -1) |> Timex.end_of_month()

    # Query for the current month's data
    current_month_query =
      TrialBalance
      |> join(:inner, [tb], cs in CustSegment, on: tb.gl_no == cs.sap_gl_acc_no)
      |> where([tb, cs], tb.date >= ^start_dt and tb.date <= ^end_dt)
      |> where([tb, cs], tb.gl_no in ^interest_gls)
      |> where([tb, cs], cs.month == ^month) # Add the month filter
      |> select([tb, cs], %{
        gl_no: tb.gl_no,
        gl_desc: tb.gl_desc,
        concatenated_gl: fragment("? + ' ' + ?", tb.gl_no, tb.gl_desc),
        actual_this_year: tb.actual_this_year,
        business_unit_grp: cs.business_unit_grp,
        ccy_cat: cs.ccy_cat,
        mdt_mvmt_in_lcy: cs.mdt_mvmt_in_lcy,
        month: cs.month # Include the month in the result
      })

    # Query for the previous month's end-of-month data
    prev_month_query =
      TrialBalance
      |> where([tb], tb.date == ^prev_month_end_dt)
      |> where([tb], tb.gl_no in ^interest_gls)
      |> select([tb], %{
        gl_no: tb.gl_no,
        prev_actual_this_year: tb.actual_this_year
      })

    # Combine the two queries to calculate the MTD value
    from(c in subquery(current_month_query),
      left_join: p in subquery(prev_month_query),
      on: c.gl_no == p.gl_no,
      select: %{
        gl_no: c.gl_no,
        gl_desc: c.gl_desc,
        concatenated_gl: c.concatenated_gl,
        actual_this_year: c.actual_this_year,
        prev_actual_this_year: coalesce(p.prev_actual_this_year, 0),
        mtd_value: c.actual_this_year - coalesce(p.prev_actual_this_year, 0),
        business_unit_grp: c.business_unit_grp,
        ccy_cat: c.ccy_cat,
        mdt_mvmt_in_lcy: c.mdt_mvmt_in_lcy,
        month: c.month # Include the month in the final result
      }
    )
    |> Repo.all()
  end

  def income_statement_gls() do
    [
      "700085", "705900", "700220", "708600", "708605", "708620", "709500",
      "700095", "700272", "700273", "703905", "706400", "702300", "735700",
      "700115", "708500", "709800", "708705", "870016", "734260", "702000",
      "734020", "734100", "723200", "700005", "728900", "728800",
      "727500", "734040", "725300", "733760", "733500", "700130", "700132",
      "700210", "703200", "706100", "721000", "754800", "742200", "763080",
      "763105", "734800", "749000", "740115", "744800", "734840", "734720",
      "734960", "744200", "762900", "741303", "734585", "757310", "757320",
      "757330", "757340", "763460", "775030", "775035", "775040", "775045",
      "775046", "775050", "775170", "775085", "775090", "775095", "775100",
      "775101", "775105", "775185", "775140", "775145", "775150", "775155",
      "775156", "775160", "775210", "775215", "775225", "775230", "775005",
      "775000", "775060", "775010", "775025", "775080", "775165", "775175",
      "775180", "775190", "471900", "472100", "492725", "494540", "800008",
      "800012", "800100", "800155", "800175", "800245", "800260", "800270",
      "800280", "800285", "800290", "800343", "800405", "801210", "804100",
      "804600", "806100", "807900", "808450", "811200", "811700", "812200",
      "812800", "813100", "813300", "813400", "813500", "813800", "813900",
      "814100", "814800", "814900", "815100", "815200", "815300", "816200",
      "816300", "816400", "816700", "816850", "816900", "817000", "817100",
      "817900", "818100", "818300", "818400", "819000", "819100", "819300",
      "833362", "821000", "821600", "822400", "823600", "823700", "823900",
      "824500", "824700", "825200", "826700", "827100", "827600", "828100",
      "828900", "832400", "832900", "835450", "835560", "835565", "870011",
      "870250", "892900", "824900", "800075", "800085", "800095", "800105",
      "834294", "834295", "834300", "834565", "840500", "842801", "843600",
      "853100", "854395", "854398", "854600", "854620", "856720", "857180",
      "857200", "857220", "866020", "834280", "834298", "898975", "898976",
      "800110", "834320", "852200", "856405", "866025", "898977", "715000",
      "715100", "715200", "714800", "747700", "747900", "748200", "833730",
      "833740", "870450", "870540", "883100", "885090", "441050", "441020",
      "441055", "441060", "441220", "771300", "771400", "771600", "772000",
      "775239", "774200", "771605", "401500", "404500", "401900", "404300",
      "404302", "406150", "407340", "407320", "407341", "492780", "495134",
      "497725", "497735", "497170", "401901", "403000", "403300", "403410",
      "401000", "403414", "401110", "403415", "407780", "407840", "402811",
      "402813", "407845", "401111", "403004", "408000", "404100",  "403101",
      "403100", "406100", "402300", "400000", "405104", "402800", "402810",
      "402812", "402819", "400017", "404800", "404900", "405100", "407100",
      "493340", "402802", "407680", "495710", "400035", "420000", "420100",
      "420200", "420300", "421200", "421300", "421100", "426300", "421400",
      "428600", "423900", "425000", "424000", "425110", "429600", "490830",
      "492260", "492940", "423600", "492280", "421800", "421900", "428400",
      "425400", "427100", "427600", "423400", "422100", "422110", "422130",
      "425600", "422120", "429700", "425800", "425620", "425700", "428200",
      "428220", "424929", "422300", "428800", "428900", "457000", "492020",
      "490720", "456600", "490760", "441640", "440180", "441200", "440220",
      "440320", "440640", "440800", "441240", "440700", "440780", "440440",
      "440620", "440860", "440880", "440920", "440450", "441000", "440750",
      "440751", "440752", "440753", "440755", "440766", "493200", "493220",
      "493260", "493280", "450500", "450700", "451800", "451600", "451500",
      "491820", "490193", "491020", "491022", "404200", "491024", "491330",
      "493100", "490030", "429800", "491044", "492860", "492740", "492800",
      "492820", "492840", "490016", "455900", "490740", "455100", "460000",
      "460100", "460700", "460900", "461000", "462200", "465400", "465900",
      "466200", "465300", "465500", "466300", "466350", "466400", "491850",
      "470005", "475300", "475700", "476200", "476300", "477200", "480000",
      "480100", "480400", "481300", "481500", "481600", "481900", "482102",
      "492960", "486300", "485000", "486000", "486200", "486600", "486700",
      "487000", "487400", "491960", "492440", "466100", "490780", "490880",
      "492020", "492180", "492320", "493160", "492600", "494300", "493780",
      "493460", "490280", "490220", "492160", "490940", "486210", "490900",
      "491250", "492892", "492896", "496957", "496959", "461500", "461501",
      "461502", "461503", "461510", "461511", "461512", "496891", "496981",
      "499291", "499292", "499293", "499294", "499295", "499296", "499297",
      "499298", "499299", "499300", "499302", "499304", "499308", "496759",
      "496866", "490218", "499020", "499060", "499260", "499025", "499035"
    ]
  end

  def get_ifrs_trend_data(date) do
    gls = income_statement_gls()
    {:ok, d} = Date.from_iso8601(date)
    year = d.year

    fetch_trial_balance_data(gls, year)
    |> group_by_gl()
    |> Enum.map(&build_gl_trend/1)
  end

  defp fetch_trial_balance_data(gls, year) do
    TrialBalance
    |> where(
      [t],
      t.gl_no in ^gls and
      fragment("YEAR(?) = ?", t.date, ^year) and
      not is_nil(t.actual_this_year)
    )
    |> select([t], %{
      gl_no: t.gl_no,
      gl_desc: t.gl_desc,
      month: fragment("MONTH(?)", t.date),
      actual: t.actual_this_year
    })
    |> Repo.all()
  end

  defp group_by_gl(data) do
    Enum.group_by(data, &{&1.gl_no, &1.gl_desc})
  end

  defp build_gl_trend({{gl_no, gl_desc}, entries}) do
    ytd_map = build_ytd_map(entries)
    monthly = build_monthly_ytd(ytd_map)

    q1 = sum_months(monthly, [1, 2, 3])
    q2 = sum_months(monthly, [4, 5, 6])
    q3 = sum_months(monthly, [7, 8, 9])
    q4 = sum_months(monthly, [10, 11, 12])

    ytd = Enum.reduce([q1, q2, q3, q4], Decimal.new(0), &Decimal.add/2)

    %{
      gl_no: gl_no,
      gl_desc: gl_desc,
      gl_no_desc: "#{gl_no} #{gl_desc}",
      january: monthly[1],
      february: monthly[2],
      march: monthly[3],
      q1: q1,
      april: monthly[4],
      may: monthly[5],
      june: monthly[6],
      q2: q2,
      july: monthly[7],
      august: monthly[8],
      september: monthly[9],
      q3: q3,
      october: monthly[10],
      november: monthly[11],
      december: monthly[12],
      q4: q4,
      ytd: ytd
    }
  end

  defp build_ytd_map(entries) do
    Enum.reduce(entries, %{}, fn %{month: m, actual: val}, acc ->
      Map.put(acc, m, val)
    end)
  end

  defp build_monthly_ytd(ytd_map) do
    Enum.reduce(1..12, %{}, fn m, acc ->
      ytd_current = Map.get(ytd_map, m)
      ytd_previous = Map.get(ytd_map, m - 1, Decimal.new(0))

      ytd_val =
        case ytd_current do
          nil -> Decimal.new(0)
          val when m == 1 -> val
          val -> Decimal.sub(val, ytd_previous)
        end

      Map.put(acc, m, ytd_val)
    end)
  end

  defp sum_months(month_map, months) do
    months
    |> Enum.map(&Map.get(month_map, &1, Decimal.new(0)))
    |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
  end


  alias MisReports.SourceData.TrialBalScrFile

  @doc """
  Returns the list of tbl_trial_bal_src_files.

  ## Examples

      iex> list_tbl_trial_bal_src_files()
      [%TrialBalScrFile{}, ...]

  """
  def list_tbl_trial_bal_src_files do
    Repo.all(TrialBalScrFile)
  end

  @doc """
  Gets a single trial_bal_scr_file.

  Raises `Ecto.NoResultsError` if the Trial bal scr file does not exist.

  ## Examples

      iex> get_trial_bal_scr_file!(123)
      %TrialBalScrFile{}

      iex> get_trial_bal_scr_file!(456)
      ** (Ecto.NoResultsError)

  """
  def get_trial_bal_scr_file!(id), do: Repo.get!(TrialBalScrFile, id)

  @doc """
  Creates a trial_bal_scr_file.

  ## Examples

      iex> create_trial_bal_scr_file(%{field: value})
      {:ok, %TrialBalScrFile{}}

      iex> create_trial_bal_scr_file(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_trial_bal_scr_file(attrs \\ %{}) do
    %TrialBalScrFile{}
    |> TrialBalScrFile.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a trial_bal_scr_file.

  ## Examples

      iex> update_trial_bal_scr_file(trial_bal_scr_file, %{field: new_value})
      {:ok, %TrialBalScrFile{}}

      iex> update_trial_bal_scr_file(trial_bal_scr_file, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_trial_bal_scr_file(%TrialBalScrFile{} = trial_bal_scr_file, attrs) do
    trial_bal_scr_file
    |> TrialBalScrFile.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a trial_bal_scr_file.

  ## Examples

      iex> delete_trial_bal_scr_file(trial_bal_scr_file)
      {:ok, %TrialBalScrFile{}}

      iex> delete_trial_bal_scr_file(trial_bal_scr_file)
      {:error, %Ecto.Changeset{}}

  """
  def delete_trial_bal_scr_file(%TrialBalScrFile{} = trial_bal_scr_file) do
    Repo.delete(trial_bal_scr_file)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking trial_bal_scr_file changes.

  ## Examples

      iex> change_trial_bal_scr_file(trial_bal_scr_file)
      %Ecto.Changeset{data: %TrialBalScrFile{}}

  """
  def change_trial_bal_scr_file(%TrialBalScrFile{} = trial_bal_scr_file, attrs \\ %{}) do
    TrialBalScrFile.changeset(trial_bal_scr_file, attrs)
  end

  def trial_balance_list(params) do
    TrialBalScrFile
    |> preload([:checker, :maker])
    |> trial_bal_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp trial_bal_isearch_filter(query, nil), do: query

  defp trial_bal_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.year, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.filename, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def get_trial_bal_pending_upload() do
    TrialBalScrFile
    |> where(status: "PENDING_UPLOAD")
    |> Repo.all()
  end

  def me() do
    list_tbl_cust_segmentation()
    |> Enum.each(fn i ->
      date =
        String.slice(i.day, 0..3) <>
          "-" <> String.slice(i.day, 4..5) <> "-" <> String.slice(i.day, 6..7)

      update_cust_segment(i, %{date: date})
    end)
  end

  # MisReports.SourceData.me()
  alias MisReports.SourceData.FxCashFlow

  @doc """
  Returns the list of tbl_live_fx_cash_flow.

  ## Examples

      iex> list_tbl_live_fx_cash_flow()
      [%FxCashFlow{}, ...]

  """
  def list_tbl_live_fx_cash_flow do
    Repo.all(FxCashFlow)
  end

  # MisReports.SourceData.get_all_fxdeal_by_date("2023-09-01", "2023-09-29")
  # def get_all_fxdeal_by_date(from, to) do
  #   FxCashFlow
  #   |> where(
  #     [a],
  #     fragment("CAST(? AS DATE) >= ?", a.report_date, ^from) and
  #     fragment("CAST(? AS DATE) <= ?", a.report_date, ^to)
  #   )
  #   |> Repo.all()
  # end

  def get_all_fxdeal_by_date(from, to) do
    FxCashFlow
    |> where(
      [a],
      fragment("EOMONTH(?) >= ?", a.report_date, ^from) and
      fragment("EOMONTH(?) <= ?", a.report_date, ^to)
    )
    |> select([a], %{
      product_type: a.product_type,
      pay_ccy: a.pay_ccy,
      receive_amount: a.receive_amount,
      mtm_base: a.mtm_base,
      counter_party: a.counter_party,
      trade_date: a.trade_date,
      cash_flow_date: a.cash_flow_date
    })
    |> Repo.all()
  end

  @doc """
  Gets a single fx_cash_flow.

  Raises `Ecto.NoResultsError` if the Fx cash flow does not exist.

  ## Examples

      iex> get_fx_cash_flow!(123)
      %FxCashFlow{}

      iex> get_fx_cash_flow!(456)
      ** (Ecto.NoResultsError)

  """
  def get_fx_cash_flow!(id), do: Repo.get!(FxCashFlow, id)

  @doc """
  Creates a fx_cash_flow.

  ## Examples

      iex> create_fx_cash_flow(%{field: value})
      {:ok, %FxCashFlow{}}

      iex> create_fx_cash_flow(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_fx_cash_flow(attrs \\ %{}) do
    %FxCashFlow{}
    |> FxCashFlow.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a fx_cash_flow.

  ## Examples

      iex> update_fx_cash_flow(fx_cash_flow, %{field: new_value})
      {:ok, %FxCashFlow{}}

      iex> update_fx_cash_flow(fx_cash_flow, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_fx_cash_flow(%FxCashFlow{} = fx_cash_flow, attrs) do
    fx_cash_flow
    |> FxCashFlow.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a fx_cash_flow.

  ## Examples

      iex> delete_fx_cash_flow(fx_cash_flow)
      {:ok, %FxCashFlow{}}

      iex> delete_fx_cash_flow(fx_cash_flow)
      {:error, %Ecto.Changeset{}}

  """
  def delete_fx_cash_flow(%FxCashFlow{} = fx_cash_flow) do
    Repo.delete(fx_cash_flow)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking fx_cash_flow changes.

  ## Examples

      iex> change_fx_cash_flow(fx_cash_flow)
      %Ecto.Changeset{data: %FxCashFlow{}}

  """
  def change_fx_cash_flow(%FxCashFlow{} = fx_cash_flow, attrs \\ %{}) do
    FxCashFlow.changeset(fx_cash_flow, attrs)
  end

  def fx_cash_flow_list(params) do
    FxCashFlow
    |> fx_cash_flow_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp fx_cash_flow_isearch_filter(query, nil), do: query

  defp fx_cash_flow_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.country, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.pay_ccy, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.product_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.trade_id, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.book, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.trade_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.near_rate, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.far_rate, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.fx_swap_leg, ^search_term))
  end

  alias MisReports.SourceData.CreditLineAvbl

  @doc """
  Returns the list of tbl_credit_line_avbl.

  ## Examples

      iex> list_tbl_credit_line_avbl()
      [%CreditLineAvbl{}, ...]

  """
  def list_tbl_credit_line_avbl do
    Repo.all(CreditLineAvbl)
  end

  @doc """
  Gets a single credit_line_avbl.

  Raises `Ecto.NoResultsError` if the Credit line avbl does not exist.

  ## Examples

      iex> get_credit_line_avbl!(123)
      %CreditLineAvbl{}

      iex> get_credit_line_avbl!(456)
      ** (Ecto.NoResultsError)

  """
  def get_credit_line_avbl!(id), do: Repo.get!(CreditLineAvbl, id)

  @doc """
  Creates a credit_line_avbl.

  ## Examples

      iex> create_credit_line_avbl(%{field: value})
      {:ok, %CreditLineAvbl{}}

      iex> create_credit_line_avbl(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_credit_line_avbl(attrs \\ %{}) do
    %CreditLineAvbl{}
    |> CreditLineAvbl.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a credit_line_avbl.

  ## Examples

      iex> update_credit_line_avbl(credit_line_avbl, %{field: new_value})
      {:ok, %CreditLineAvbl{}}

      iex> update_credit_line_avbl(credit_line_avbl, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_credit_line_avbl(%CreditLineAvbl{} = credit_line_avbl, attrs) do
    credit_line_avbl
    |> CreditLineAvbl.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a credit_line_avbl.

  ## Examples

      iex> delete_credit_line_avbl(credit_line_avbl)
      {:ok, %CreditLineAvbl{}}

      iex> delete_credit_line_avbl(credit_line_avbl)
      {:error, %Ecto.Changeset{}}

  """
  def delete_credit_line_avbl(%CreditLineAvbl{} = credit_line_avbl) do
    Repo.delete(credit_line_avbl)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking credit_line_avbl changes.

  ## Examples

      iex> change_credit_line_avbl(credit_line_avbl)
      %Ecto.Changeset{data: %CreditLineAvbl{}}

  """
  def change_credit_line_avbl(%CreditLineAvbl{} = credit_line_avbl, attrs \\ %{}) do
    CreditLineAvbl.changeset(credit_line_avbl, attrs)
  end

  def get_credit_lines_by_year(year) do
    CreditLineAvbl
    |> where([a], fragment("YEAR(?) = ?", a.inserted_at, ^year))
    |> Repo.all()
  end

  alias MisReports.SourceData.RawSchedules

  @doc """
  Returns the list of tbl_raw_schdedules.

  ## Examples

      iex> list_tbl_raw_schdedules()
      [%RawSchedules{}, ...]

  """
  def list_tbl_raw_schdedules do
    Repo.all(RawSchedules)
  end

  @doc """
  Gets a single raw_schedules.

  Raises `Ecto.NoResultsError` if the Raw schedules does not exist.

  ## Examples

      iex> get_raw_schedules!(123)
      %RawSchedules{}

      iex> get_raw_schedules!(456)
      ** (Ecto.NoResultsError)

  """
  def get_raw_schedules!(id), do: Repo.get!(RawSchedules, id)

  @doc """
  Creates a raw_schedules.

  ## Examples

      iex> create_raw_schedules(%{field: value})
      {:ok, %RawSchedules{}}

      iex> create_raw_schedules(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_raw_schedules(attrs \\ %{}) do
    %RawSchedules{}
    |> RawSchedules.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a raw_schedules.

  ## Examples

      iex> update_raw_schedules(raw_schedules, %{field: new_value})
      {:ok, %RawSchedules{}}

      iex> update_raw_schedules(raw_schedules, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_raw_schedules(%RawSchedules{} = raw_schedules, attrs) do
    raw_schedules
    |> RawSchedules.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a raw_schedules.

  ## Examples

      iex> delete_raw_schedules(raw_schedules)
      {:ok, %RawSchedules{}}

      iex> delete_raw_schedules(raw_schedules)
      {:error, %Ecto.Changeset{}}

  """
  def delete_raw_schedules(%RawSchedules{} = raw_schedules) do
    Repo.delete(raw_schedules)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking raw_schedules changes.

  ## Examples

      iex> change_raw_schedules(raw_schedules)
      %Ecto.Changeset{data: %RawSchedules{}}

  """
  def change_raw_schedules(%RawSchedules{} = raw_schedules, attrs \\ %{}) do
    RawSchedules.changeset(raw_schedules, attrs)
  end

  def get_schedule(schedule) do
    RawSchedules
    |> where(
      [a],
      # fragment("CAST(? AS DATE) >= ?", a.end_date, ^date) and
      # fragment("CAST(? AS DATE) <= ?", a.end_date, ^date) and
      a.schedule == ^schedule
    )
    |> limit(1)
    |> Repo.one()
  end

  def get_schedule(schedule, date) do
    RawSchedules
    |> where(
      [a],
      fragment("CAST(? AS DATE) = ?", a.end_date, ^date) and
        a.schedule == ^schedule
    )
    |> limit(1)
    |> Repo.one()
  end

  def income_statement_gls() do
    [
      "725300",
      "733500",
      "733760",
      "M305053",
      "700130",
      "700132",
      "M305054",
      "M305051",
      "M305050",
      "715000",
      "715100",
      "715200",
      "M305411",
      "714800",
      "M305420",
      "M305090",
      "M305060",
      "734020",
      "734100",
      "M305062",
      "723200",
      "M305063",
      "700085",
      "705900",
      "708600",
      "708605",
      "708620",
      "709500",
      "M305064",
      "700005",
      "700095",
      "700272",
      "700273",
      "703905",
      "706400",
      "727500",
      "M305065",
      "702300",
      "735700",
      "M305066",
      "706100",
      "M305067",
      "708500",
      "709800",
      "M305081",
      "708705",
      "M305082",
      "M305068",
      "703200",
      "721000",
      "870016",
      "M305069",
      "734260",
      "M305072",
      "734040",
      "M305451",
      "702000",
      "M305452",
      "728800",
      "M305453",
      "M305000",
      "734840",
      "M311110",
      "734720",
      "734800",
      "749000",
      "M311115",
      "734585",
      "M311145",
      "735080",
      "M311150",
      "740115",
      "744800",
      "M311171",
      "742200",
      "763105",
      "M311172",
      "M311170",
      "754800",
      "M311175",
      "734960",
      "744200",
      "M311180",
      "757310",
      "757320",
      "757330",
      "757340",
      "M311215",
      "747700",
      "747900",
      "748200",
      "M311711",
      "M311710",
      "M311721",
      "762900",
      "M311752",
      "741303",
      "M311755",
      "763460",
      "M311764",
      "M311750",
      "M311000",
      "M304100",
      "800155",
      "800245",
      "800280",
      "804600",
      "806100",
      "813300",
      "813400",
      "813500",
      "813900",
      "814100",
      "814800",
      "814900",
      "815100",
      "815200",
      "815300",
      "M317051",
      "800405",
      "813800",
      "823900",
      "832900",
      "835565",
      "M317300",
      "M317050",
      "800175",
      "800260",
      "800270",
      "800285",
      "800290",
      "801210",
      "808450",
      "816200",
      "816300",
      "816400",
      "870011",
      "811200",
      "813100",
      "816700",
      "816850",
      "816900",
      "817000",
      "817100",
      "817900",
      "M317150",
      "818300",
      "818400",
      "819100",
      "M317200",
      "807900",
      "812800",
      "821600",
      "822400",
      "823600",
      "823700",
      "835450",
      "835560",
      "892900",
      "M317251",
      "M317250",
      "811800",
      "824500",
      "824700",
      "824900",
      "825200",
      "M317350",
      "819300",
      "M317450",
      "800012",
      "800100",
      "804100",
      "811700",
      "827600",
      "828100",
      "828900",
      "870250",
      "M317551",
      "800343",
      "819000",
      "M317552",
      "M317550",
      "M317590",
      "M317010",
      "800008",
      "M317580",
      "492725",
      "494540",
      "M317610",
      "818100",
      "M317640",
      "471900",
      "472100",
      "821000",
      "832400",
      "M317650",
      "M317600",
      "M317000",
      "800075",
      "800095",
      "834294",
      "840500",
      "853100",
      "M319610",
      "800110",
      "M319620",
      "800085",
      "800105",
      "834295",
      "M319630",
      "M319600",
      "856720",
      "866020",
      "898975",
      "M319910",
      "854600",
      "M319911",
      "834320",
      "852200",
      "856405",
      "866025",
      "898977",
      "M319920",
      "834300",
      "854620",
      "898976",
      "M319930",
      "M319900",
      "834565",
      "842801",
      "843600",
      "857180",
      "M320260",
      "854395",
      "854398",
      "M320611",
      "M320250",
      "M319000",
      "833730",
      "833740",
      "870450",
      "870540",
      "M322800",
      "M321011",
      "883100",
      "M322300",
      "M321010",
      "885090",
      "M324500",
      "M324000",
      "M316000",
      "775000",
      "775005",
      "775010",
      "M313511",
      "775025",
      "M313513",
      "775030",
      "775035",
      "775040",
      "775045",
      "775046",
      "775050",
      "775170",
      "M313514",
      "M313512",
      "M313510",
      "775060",
      "M313521",
      "775080",
      "M313523",
      "775085",
      "775090",
      "775095",
      "775100",
      "775101",
      "775105",
      "775185",
      "M313524",
      "M313522",
      "M313520",
      "775140",
      "775145",
      "775150",
      "775155",
      "775156",
      "M313534",
      "771300",
      "771400",
      "771600",
      "772000",
      "M313536",
      "775239",
      "M313537",
      "M313532",
      "M313530",
      "M313500",
      "775165",
      "775175",
      "M313610",
      "775180",
      "775190",
      "M313620",
      "M313600",
      "774200",
      "M314241",
      "M314240",
      "771605",
      "M314252",
      "M314250",
      "M314000",
      "M313000",
      "M307000",
      "497725",
      "497170",
      "495134",
      "492780",
      "406150",
      "406100",
      "403100",
      "402300",
      "401900",
      "401500",
      "M326105",
      "M326100",
      "400000",
      "404100",
      "404300",
      "405104",
      "407320",
      "407340",
      "M326150",
      "M326005",
      "408000",
      "M326110",
      "403000",
      "M326125",
      "402800",
      "402810",
      "402812",
      "402819",
      "M326127",
      "M326120",
      "401110",
      "M326128",
      "403300",
      "403410",
      "M326122",
      "M326131",
      "401000",
      "403414",
      "403415",
      "M326129",
      "407845",
      "M326135",
      "M326132",
      "M326121",
      "M326130",
      "400017",
      "404800",
      "404900",
      "405100",
      "493340",
      "M326200",
      "402802",
      "403004",
      "403101",
      "404302",
      "404500",
      "407680",
      "M326300",
      "407100",
      "M326400",
      "M326000",
      "420000",
      "420100",
      "420200",
      "420300",
      "421200",
      "421300",
      "421400",
      "423900",
      "425000",
      "425110",
      "490830",
      "492260",
      "492280",
      "492940",
      "M328100",
      "423600",
      "M328150",
      "421800",
      "421900",
      "428400",
      "M328210",
      "425400",
      "427100",
      "427600",
      "M328220",
      "423400",
      "M328230",
      "422100",
      "422110",
      "422120",
      "422130",
      "425600",
      "425620",
      "425700",
      "428200",
      "M328240",
      "422300",
      "M328250",
      "428800",
      "428900",
      "M328260",
      "M328200",
      "424929",
      "M329710",
      "M328000",
      "441640",
      "M329605",
      "440180",
      "441200",
      "M329615",
      "440220",
      "440320",
      "440640",
      "440800",
      "441240",
      "M329620",
      "440700",
      "M329625",
      "440780",
      "M329630",
      "440440",
      "440620",
      "440860",
      "440880",
      "440920",
      "441000",
      "M329635",
      "440750",
      "440751",
      "440752",
      "440753",
      "M329660",
      "440755",
      "M329665",
      "440766",
      "M329681",
      "M329600",
      "490016",
      "M329700",
      "451500",
      "M332030",
      "450500",
      "450700",
      "M332040",
      "490193",
      "M332050",
      "451800",
      "M332011",
      "451600",
      "M332013",
      "M332010",
      "M332000",
      "404200",
      "491330",
      "493100",
      "M332220",
      "M332200",
      "493200",
      "493220",
      "493260",
      "493280",
      "M332420",
      "493230",
      "M332450",
      "M332400",
      "490030",
      "M332620",
      "M332600",
      "429800",
      "491044",
      "492740",
      "492800",
      "492820",
      "492840",
      "492860",
      "M332720",
      "M332700",
      "455900",
      "M340010",
      "456600",
      "457000",
      "M340020",
      "455100",
      "M340030",
      "490760",
      "M340040",
      "490740",
      "M340070",
      "M340000",
      "460000",
      "460100",
      "460700",
      "460900",
      "461000",
      "462200",
      "M340120",
      "M340100",
      "465400",
      "465500",
      "465900",
      "466200",
      "466300",
      "466350",
      "466400",
      "M340220",
      "491850",
      "M340250",
      "M340200",
      "470005",
      "M340320",
      "M340300",
      "475300",
      "475700",
      "476200",
      "476300",
      "477200",
      "M340420",
      "M340400",
      "480000",
      "480100",
      "480400",
      "481300",
      "481500",
      "481600",
      "481900",
      "482102",
      "492960",
      "M340520",
      "M340500",
      "486300",
      "M340601",
      "485000",
      "486000",
      "486200",
      "486600",
      "486700",
      "487000",
      "487400",
      "491960",
      "492440",
      "M340602",
      "M340600",
      "491820",
      "M340621",
      "M340620",
      "466100",
      "490780",
      "490880",
      "492020",
      "492180",
      "492320",
      "493160",
      "493460",
      "M300701",
      "493780",
      "M300703",
      "492160",
      "M300705",
      "490940",
      "M300707",
      "490280",
      "M300709",
      "490900",
      "M300713",
      "491020",
      "491024",
      "M300715",
      "490220",
      "M300716",
      "491022",
      "M300718",
      "494300",
      "M300722",
      "486210",
      "M300726",
      "M340750",
      "491250",
      "M340801",
      "492892",
      "492896",
      "M340802",
      "496957",
      "496959",
      "M340803",
      "461500",
      "461501",
      "461502",
      "461503",
      "461510",
      "461511",
      "461512",
      "496891",
      "496981",
      "499291",
      "499292",
      "499293",
      "499294",
      "499295",
      "499296",
      "499297",
      "499298",
      "499299",
      "499300",
      "499302",
      "499304",
      "499308",
      "M340804",
      "M340800",
      "M327100",
      "496866",
      "M327208",
      "M327001",
      "490218",
      "M327226",
      "M327002",
      "M327200",
      "M335000",
      "M306000",
      "441050",
      "441055",
      "441060",
      "M329640",
      "M329650",
      "M348000",
      "M330000",
      "499025",
      "M346022",
      "M346020",
      "499035",
      "M346065",
      "M346060",
      "M346000",
      "499020",
      "M353100",
      "499260",
      "M353400",
      "M353000",
      "M360000",
      "492310",
      "M392000",
      "870500",
      "M393000",
      "M391500"
    ]
  end

  alias MisReports.SourceData.WeeklyReportSrcFile

  @doc """
  Returns the list of weekly_report_src_files.

  ## Examples

      iex> list_weekly_report_src_files()
      [%WeeklyReportSrcFile{}, ...]

  """
  def list_weekly_report_src_files do
    Repo.all(WeeklyReportSrcFile)
  end

  alias MisReports.SourceData.WeeklyReportSrcFile

  @doc """
  Returns the list of weekly_report_src_files.

  ## Examples

      iex> list_weekly_report_src_files()
      [%WeeklyReportSrcFile{}, ...]

  """
  def list_weekly_report_src_files do
    Repo.all(WeeklyReportSrcFile)
  end

  @doc """
  Gets a single weekly_report_src_file.

  Raises `Ecto.NoResultsError` if the Weekly report src file does not exist.

  ## Examples

      iex> get_weekly_report_src_file!(123)
      %WeeklyReportSrcFile{}

      iex> get_weekly_report_src_file!(456)
      ** (Ecto.NoResultsError)

  """
  def get_weekly_report_src_file!(id), do: Repo.get!(WeeklyReportSrcFile, id)

  def get_weekly_report_src_file_pending_upload() do
    WeeklyReportSrcFile
    |> where(status: "PENDING_UPLOAD")
    |> Repo.all()
  end

  @doc """
  Creates a weekly_report_src_file.

  ## Examples

      iex> create_weekly_report_src_file(%{field: value})
      {:ok, %WeeklyReportSrcFile{}}

      iex> create_weekly_report_src_file(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_weekly_report_src_file(attrs \\ %{}) do
    %WeeklyReportSrcFile{}
    |> WeeklyReportSrcFile.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a weekly_report_src_file.

  ## Examples

      iex> update_weekly_report_src_file(weekly_report_src_file, %{field: new_value})
      {:ok, %WeeklyReportSrcFile{}}

      iex> update_weekly_report_src_file(weekly_report_src_file, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_weekly_report_src_file(%WeeklyReportSrcFile{} = weekly_report_src_file, attrs) do
    weekly_report_src_file
    |> WeeklyReportSrcFile.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a weekly_report_src_file.

  ## Examples

      iex> delete_weekly_report_src_file(weekly_report_src_file)
      {:ok, %WeeklyReportSrcFile{}}

      iex> delete_weekly_report_src_file(weekly_report_src_file)
      {:error, %Ecto.Changeset{}}

  """
  def delete_weekly_report_src_file(%WeeklyReportSrcFile{} = weekly_report_src_file) do
    Repo.delete(weekly_report_src_file)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking weekly_report_src_file changes.

  ## Examples

      iex> change_weekly_report_src_file(weekly_report_src_file)
      %Ecto.Changeset{data: %WeeklyReportSrcFile{}}

  """
  def change_weekly_report_src_file(%WeeklyReportSrcFile{} = weekly_report_src_file, attrs \\ %{}) do
    WeeklyReportSrcFile.changeset(weekly_report_src_file, attrs)
  end

  def weekly_upload_list(params) do
    WeeklyReportSrcFile
    |> preload([:checker, :maker])
    |> weekly_upload_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp weekly_upload_isearch_filter(query, nil), do: query

  defp weekly_upload_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.year, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.filename, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  alias MisReports.SourceData.WeeklyReportEntriesAllDeal

  @doc """
  Returns the list of weekly_report_entries_all_deal.

  ## Examples

      iex> list_weekly_report_entries_all_deal()
      [%WeeklyReportEntriesAllDeal{}, ...]

  """
  def list_weekly_report_entries_all_deal do
    Repo.all(WeeklyReportEntriesAllDeal)
  end

  @doc """
  Gets a single weekly_report_entries_all_deal.

  Raises `Ecto.NoResultsError` if the Weekly report entries all deal does not exist.

  ## Examples

      iex> get_weekly_report_entries_all_deal!(123)
      %WeeklyReportEntriesAllDeal{}

      iex> get_weekly_report_entries_all_deal!(456)
      ** (Ecto.NoResultsError)

  """
  def get_weekly_report_entries_all_deal!(id), do: Repo.get!(WeeklyReportEntriesAllDeal, id)

  @doc """
  Creates a weekly_report_entries_all_deal.

  ## Examples

      iex> create_weekly_report_entries_all_deal(%{field: value})
      {:ok, %WeeklyReportEntriesAllDeal{}}

      iex> create_weekly_report_entries_all_deal(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_weekly_report_entries_all_deal(attrs \\ %{}) do
    %WeeklyReportEntriesAllDeal{}
    |> WeeklyReportEntriesAllDeal.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a weekly_report_entries_all_deal.

  ## Examples

      iex> update_weekly_report_entries_all_deal(weekly_report_entries_all_deal, %{field: new_value})
      {:ok, %WeeklyReportEntriesAllDeal{}}

      iex> update_weekly_report_entries_all_deal(weekly_report_entries_all_deal, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_weekly_report_entries_all_deal(
        %WeeklyReportEntriesAllDeal{} = weekly_report_entries_all_deal,
        attrs
      ) do
    weekly_report_entries_all_deal
    |> WeeklyReportEntriesAllDeal.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a weekly_report_entries_all_deal.

  ## Examples

      iex> delete_weekly_report_entries_all_deal(weekly_report_entries_all_deal)
      {:ok, %WeeklyReportEntriesAllDeal{}}

      iex> delete_weekly_report_entries_all_deal(weekly_report_entries_all_deal)
      {:error, %Ecto.Changeset{}}

  """
  def delete_weekly_report_entries_all_deal(
        %WeeklyReportEntriesAllDeal{} = weekly_report_entries_all_deal
      ) do
    Repo.delete(weekly_report_entries_all_deal)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking weekly_report_entries_all_deal changes.

  ## Examples

      iex> change_weekly_report_entries_all_deal(weekly_report_entries_all_deal)
      %Ecto.Changeset{data: %WeeklyReportEntriesAllDeal{}}

  """
  def change_weekly_report_entries_all_deal(
        %WeeklyReportEntriesAllDeal{} = weekly_report_entries_all_deal,
        attrs \\ %{}
      ) do
    WeeklyReportEntriesAllDeal.changeset(weekly_report_entries_all_deal, attrs)
  end

  alias MisReports.SourceData.WeeklyReportEntriesCustContribution

  @doc """
  Returns the list of weekly_report_entries_cust_contribution.

  ## Examples

      iex> list_weekly_report_entries_cust_contribution()
      [%WeeklyReportEntriesCustContribution{}, ...]

  """
  def list_weekly_report_entries_cust_contribution do
    Repo.all(WeeklyReportEntriesCustContribution)
  end

  @doc """
  Gets a single weekly_report_entries_cust_contribution.

  Raises `Ecto.NoResultsError` if the Weekly report entries cust contribution does not exist.

  ## Examples

      iex> get_weekly_report_entries_cust_contribution!(123)
      %WeeklyReportEntriesCustContribution{}

      iex> get_weekly_report_entries_cust_contribution!(456)
      ** (Ecto.NoResultsError)

  """
  def get_weekly_report_entries_cust_contribution!(id),
    do: Repo.get!(WeeklyReportEntriesCustContribution, id)

  @doc """
  Creates a weekly_report_entries_cust_contribution.

  ## Examples

      iex> create_weekly_report_entries_cust_contribution(%{field: value})
      {:ok, %WeeklyReportEntriesCustContribution{}}

      iex> create_weekly_report_entries_cust_contribution(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_weekly_report_entries_cust_contribution(attrs \\ %{}) do
    %WeeklyReportEntriesCustContribution{}
    |> WeeklyReportEntriesCustContribution.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a weekly_report_entries_cust_contribution.

  ## Examples

      iex> update_weekly_report_entries_cust_contribution(weekly_report_entries_cust_contribution, %{field: new_value})
      {:ok, %WeeklyReportEntriesCustContribution{}}

      iex> update_weekly_report_entries_cust_contribution(weekly_report_entries_cust_contribution, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_weekly_report_entries_cust_contribution(
        %WeeklyReportEntriesCustContribution{} = weekly_report_entries_cust_contribution,
        attrs
      ) do
    weekly_report_entries_cust_contribution
    |> WeeklyReportEntriesCustContribution.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a weekly_report_entries_cust_contribution.

  ## Examples

      iex> delete_weekly_report_entries_cust_contribution(weekly_report_entries_cust_contribution)
      {:ok, %WeeklyReportEntriesCustContribution{}}

      iex> delete_weekly_report_entries_cust_contribution(weekly_report_entries_cust_contribution)
      {:error, %Ecto.Changeset{}}

  """
  def delete_weekly_report_entries_cust_contribution(
        %WeeklyReportEntriesCustContribution{} = weekly_report_entries_cust_contribution
      ) do
    Repo.delete(weekly_report_entries_cust_contribution)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking weekly_report_entries_cust_contribution changes.

  ## Examples

      iex> change_weekly_report_entries_cust_contribution(weekly_report_entries_cust_contribution)
      %Ecto.Changeset{data: %WeeklyReportEntriesCustContribution{}}

  """
  def change_weekly_report_entries_cust_contribution(
        %WeeklyReportEntriesCustContribution{} = weekly_report_entries_cust_contribution,
        attrs \\ %{}
      ) do
    WeeklyReportEntriesCustContribution.changeset(weekly_report_entries_cust_contribution, attrs)
  end

  alias MisReports.SourceData.GmoTpins

  @doc """
  Returns the list of gmo_tpins.

  ## Examples

      iex> list_gmo_tpins()
      [%GmoTpins{}, ...]

  """
  def list_gmo_tpins do
    Repo.all(GmoTpins)
  end

  @doc """
  Gets a single gmo_tpins.

  Raises `Ecto.NoResultsError` if the Gmo tpins does not exist.

  ## Examples

      iex> get_gmo_tpins!(123)
      %GmoTpins{}

      iex> get_gmo_tpins!(456)
      ** (Ecto.NoResultsError)

  """
  def get_gmo_tpins!(id), do: Repo.get!(GmoTpins, id)

  @doc """
  Creates a gmo_tpins.

  ## Examples

      iex> create_gmo_tpins(%{field: value})
      {:ok, %GmoTpins{}}

      iex> create_gmo_tpins(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_gmo_tpins(attrs \\ %{}) do
    %GmoTpins{}
    |> GmoTpins.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a gmo_tpins.

  ## Examples

      iex> update_gmo_tpins(gmo_tpins, %{field: new_value})
      {:ok, %GmoTpins{}}

      iex> update_gmo_tpins(gmo_tpins, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_gmo_tpins(%GmoTpins{} = gmo_tpins, attrs) do
    gmo_tpins
    |> GmoTpins.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a gmo_tpins.

  ## Examples

      iex> delete_gmo_tpins(gmo_tpins)
      {:ok, %GmoTpins{}}

      iex> delete_gmo_tpins(gmo_tpins)
      {:error, %Ecto.Changeset{}}

  """
  def delete_gmo_tpins(%GmoTpins{} = gmo_tpins) do
    Repo.delete(gmo_tpins)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking gmo_tpins changes.

  ## Examples

      iex> change_gmo_tpins(gmo_tpins)
      %Ecto.Changeset{data: %GmoTpins{}}

  """
  def change_gmo_tpins(%GmoTpins{} = gmo_tpins, attrs \\ %{}) do
    GmoTpins.changeset(gmo_tpins, attrs)
  end

  alias MisReports.Utilities

  # def get_ytd_weekly(date, currency \\ "", schedule \\ "", optional_accounts \\ []) do
  #   gls =
  #     case schedule do
  #       "27a1" ->
  #         get_bal_sap_ledger_no_27a1()

  #       "27a2" ->
  #         get_bal_sap_ledger_no_27a1()

  #       "27a3" ->
  #         get_bal_sap_ledger_no_27a3()

  #       "27a4" ->
  #         get_bal_sap_ledger_no_27a3()

  #       _ ->
  #         []
  #     end


  #       optional_accounts =
  #          case schedule do
  #            "27a5" ->
  #             get_acc_nso_27a5()
  #             _->
  #               optional_accounts
  #          end

  #          IO.inspect(optional_accounts, label: "============optional_accounts==========")

  #   cur_date = Date.from_iso8601!(date)
  #   prev_date = Timex.shift(cur_date, weeks: -1)

  #   cur_usd_rate =
  #     Utilities.get_exchange_rate_by_date_and_code(cur_date, "USD")[:exchange_rate_lcy] || "1"

  #   prev_usd_rate =
  #     Utilities.get_exchange_rate_by_date_and_code(prev_date, "USD")[:exchange_rate_lcy] || "1"

  #   {current_entries, previous_entries} =
  #     case currency do
  #       "" ->
  #         IO.inspect(currency, label: "============currency==========")

  #       _ ->
  #         build_base_query = fn date ->
  #           WeeklyReportEntriesCustContribution
  #           |> join(:left, [a], g in "gmo_tpins",
  #             on: a.customer_number_local_cif == g.customer_number
  #           )
  #           |> where([a, g], a.date == ^date)
  #           |> where([a, g], not is_nil(a.date))
  #         end

  #         apply_optional_filters = fn query ->
  #           query
  #           |> then(fn q ->
  #             if optional_accounts != [] do
  #               where(q, [a, g], a.account_number in ^optional_accounts)
  #             else
  #               q
  #             end
  #           end)
  #           |> then(fn q ->
  #             if gls != [] do
  #               where(q, [a, g], a.balance_sap_ledger_no in ^gls)
  #             else
  #               q
  #             end
  #           end)
  #         end

  #         apply_schedule_filters = fn query ->
  #           query
  #           |> then(fn q ->
  #             case schedule do
  #               s when s in ["27a1", "27a2", "27b1", "27b2"] ->
  #                 q

  #               s when s in ["27a3", "27a4"] ->
  #                 where(q, [a, g], not like(a.source_system, "AE Financial Journals"))

  #               _ ->
  #                 q
  #             end
  #           end)
  #           |> then(fn q ->
  #             case schedule do
  #               s when s in ["27a2", "27b2", "27a4"] ->
  #                 where(q, [a, g], a.currency_code != ^currency)

  #               s when s in ["27a5"] ->
  #                 q
  #               _ ->
  #                 where(q, [a, g], a.currency_code == ^currency)
  #             end
  #           end)
  #         end

  #         select_fields = fn query ->
  #           select(query, [a, g], %{
  #             acc_no: a.account_number,
  #             source_system: a.source_system,
  #             bal_num: a.balance_sap_ledger_no,
  #             acc_name: a.account_name,
  #             actual_credit_balance: a.actual_credit_balance,
  #             actual_debit_balance: a.actual_debit_balance,
  #             currency_code: a.currency_code,
  #             effective_debit_rate: a.effective_debit_rate,
  #             tpin: g.tax_identification_number
  #           })
  #         end

  #         current_entries =
  #           build_base_query.(cur_date)
  #           |> apply_optional_filters.()
  #           |> apply_schedule_filters.()
  #           |> select_fields.()
  #           |> Repo.all()

  #         previous_entries =
  #           case {cur_date.month, cur_date.day} do
  #             {1, 1} ->
  #               []

  #             _ ->
  #               build_base_query.(prev_date)
  #               |> apply_optional_filters.()
  #               |> apply_schedule_filters.()
  #               |> select_fields.()
  #               |> Repo.all()
  #           end

  #         {current_entries, previous_entries}
  #     end

  #     # IO.inspect(current_entries, label: "============current_entries==========")
  #     # IO.inspect(previous_entries, label: "============current_entries==========")
  #   # Create the map from previous_entries and current_entries
  #   previous_entries_map = Map.new(previous_entries, fn entry -> {entry.acc_no, entry} end)
  #   current_entries_map = Map.new(current_entries, fn entry -> {entry.acc_no, entry} end)
  #   # Combine all acc_nos from both previous and current entries
  #   all_acc_nos =
  #     previous_entries
  #     |> Enum.map(& &1.acc_no)
  #     |> Enum.concat(Enum.map(current_entries, & &1.acc_no))
  #     |> Enum.uniq()

  #   # Process entries, ensuring every acc_no is compared
  #   Enum.map(all_acc_nos, fn acc_no ->
  #     # Fetch entries or default to zero
  #     previous_entry =
  #       Map.get(previous_entries_map, acc_no, %{
  #         acc_no: acc_no,
  #         actual_credit_balance: Decimal.new(0),
  #         actual_debit_balance: Decimal.new(0)
  #       })

  #     current_entry =
  #       Map.get(current_entries_map, acc_no, %{
  #         acc_no: acc_no,
  #         actual_credit_balance: Decimal.new(0),
  #         actual_debit_balance: Decimal.new(0)
  #       })


  #     {prev_actual_this_year, current_actual_this_year} =
  #       case schedule do
  #         schedule when schedule in ["27a1", "27a2", "27b1", "27b2", "27a5"] ->
  #           get_balance_values(previous_entry, current_entry, :credit)

  #         schedule when schedule in ["27a3", "27a4"] ->
  #           get_balance_values(previous_entry, current_entry, :debit)

  #         _ ->
  #           {}
  #       end

  #     actual_this_year_difference =
  #       case schedule do
  #         schedule when schedule in ["27a1", "27a3", "27a1"] ->
  #           Decimal.sub(current_actual_this_year, prev_actual_this_year)

  #         schedule when schedule in ["27a2", "27b2", "27a4"] ->
  #           usd_cur = convert(current_actual_this_year, cur_usd_rate)|> Decimal.round(2)
  #           usd_prev = convert(prev_actual_this_year, prev_usd_rate)|> Decimal.round(2)
  #           Decimal.sub(usd_cur, usd_prev)
  #         _ ->
  #           Decimal.sub(current_actual_this_year, prev_actual_this_year)
  #       end

  #       # check if current_entry is a dummy and swap with previous_entry
  #        entry =
  #       case Map.has_key?(current_entry, :bal_num) do
  #         false -> previous_entry
  #         _ -> current_entry
  #       end

  #       #  convert current_actual_this_year to USD if schedule is 27b2 or 27a5 and currency is USD
  #       current_actual_this_year =
  #       case schedule do
  #         schedule when schedule in [ "27b2"] ->
  #         convert(current_actual_this_year, cur_usd_rate)|> Decimal.round(2)

  #         schedule when schedule in [ "27a5"] and entry.currency_code == "USD" ->
  #         convert(current_actual_this_year, cur_usd_rate)|> Decimal.round(2)
  #         _->
  #           current_actual_this_year
  #       end

  #       # get activity based on schedule
  #     activity =
  #       case schedule do
  #         "27a1" ->
  #           gen_activity_data_27a1(prev_actual_this_year, current_actual_this_year)

  #         "27a2" ->
  #           gen_activity_data_27a1(prev_actual_this_year, current_actual_this_year)

  #         "27a3" ->
  #           gen_activity_data_27a3(prev_actual_this_year, current_actual_this_year)

  #         "27a4" ->
  #           gen_activity_data_27a3(prev_actual_this_year, current_actual_this_year)

  #         _ ->
  #           []
  #       end



  #     %{
  #       acc_no: entry.acc_no,
  #       bal_leg_num: entry.bal_num,
  #       acc_name: entry.acc_name,
  #       tpin: entry.tpin,
  #       currency_code: entry.currency_code,
  #       effective_debit_rate: entry.effective_debit_rate,
  #       actual_this_year: current_actual_this_year,
  #       prev_actual_this_year: prev_actual_this_year,
  #       actual_this_year_difference: Decimal.abs(actual_this_year_difference),
  #       activity_type: activity
  #     }
  #   end)
  # end

 @cache_ttl :timer.minutes(2)

 def sum_actual_this_year(data) do
  Decimal.abs(data
  |> Enum.reduce(Decimal.new(0), fn item, acc ->
    Decimal.add(acc, item.actual_this_year)
  end))
end


defp get_kwacha_deposit_liabilities_public(date_string) do
  key = {date_string, "ZMW", "27a1"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
end

defp get_add_kwacha_deposit_liabilities_public(date_string) do
  key = {date_string, "ZMW", "27a1"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
  # add something
  |> Decimal.add(0)
end

defp get_fcy_deposit_liability_public(date_string) do
  key = {date_string, "ZMW", "27a2"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
  |> Decimal.div(Cachex.get!(:app_prereqs, "cur_usd_rate"))
end
defp get_kwacha_loans_advances_outstanding(date_string) do
  key = {date_string, "ZMW", "27a3"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
end
defp get_fyc_loans_outstanding(date_string) do
  key = {date_string, "ZMW", "27a4"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
  |> Decimal.div(Cachex.get!(:app_prereqs, "cur_usd_rate"))
end

defp get_fcy_deposit_foreign_vostro(date_string) do
  key = {date_string, "ALL", "27a5"}
  data = Cachex.get!(:app_prereqs, key)
  # Filter for ZMW accounts only
  IO.inspect(data, label: "============= for 27a5 data=================")
  data
  |> Enum.filter(fn item -> item.currency_code != "ZMW" end)
  |> Enum.reduce(Decimal.new("0"), fn item, acc ->
    Decimal.add(acc, item.actual_this_year)
  |> Decimal.abs()
  end)
end

defp get_kwacha_deposit_foreign_vostro(date_string) do
  key = {date_string, "ALL", "27a5"}
  data = Cachex.get!(:app_prereqs, key)
  # Filter for ZMW accounts only
  data
  |> Enum.filter(fn item -> item.currency_code == "ZMW" end)
  |> Enum.reduce(Decimal.new("0"), fn item, acc ->
    Decimal.add(acc, item.actual_this_year)
  |> Decimal.abs()
  end)
end

defp get_total_kwacha_govt(date_string) do
  key = {date_string, "ZMW", "27b1"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
end

defp get_fcy_deposit_govt(date_string) do
  key = {date_string, "ZMW", "27b2"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
end

defp get_kwacha_deposit_foreign_govt_less_agr(date_string) do
  get_kwacha_deposit_liabilities_public(date_string)
  |> Decimal.add(get_kwacha_deposit_foreign_vostro(date_string))
  |> Decimal.add( get_total_kwacha_govt(date_string))
  |> Decimal.abs()
end

defp get_fyc_deposit_foreign_govt_less_agr(date_string) do
  get_fcy_deposit_liability_public(date_string)
  |> Decimal.add(get_fcy_deposit_foreign_vostro(date_string))
  |> Decimal.add( get_fcy_deposit_govt(date_string))
  |> Decimal.abs()
end

defp get_total_core_liquid(date_string, inital_map) do
  get_notes_and_coins_value(date_string)
  |> Decimal.add(get_Tbills_holdings_at_face_value(date_string))
  |> Decimal.add(inital_map.current_account_boz)
  |> Decimal.add(inital_map.col_interbank_loans_received)
  |> Decimal.add(inital_map.col_interbank_loans_made)
  |> Decimal.add(inital_map.omo_term_deposits)
  |> Decimal.add(inital_map.omo_repo_placements)
  |> Decimal.abs()
end


defp get_finnacle_sap_gl() do
  [
    "**********",
    "**********",
    "**********",
    "**********",
    "**********"
  ]
end

defp get_govt_bond_acc() do
  [
    "ZM1000006370",
    "ZM1000006388",
    "ZM1000006396"
  ]
end

defp get_all_deals_data(date_string) do
  all_deals = MisReports.SourceData.DailyAllDeal
    |> where([w], w.date == ^date_string and w.prod_type in ["Bond", "BondMMDiscount"])
    |> Repo.all()

  # Split into separate lists using Enum.split_with
  {bond_deals, bond_mm_deals} = Enum.split_with(all_deals, fn deal ->
    deal.prod_type == "Bond" and deal.isin in get_govt_bond_acc()
  end)

  Cachex.put(:app_prereqs, "bond_mm_deals", bond_mm_deals, ttl: @cache_ttl)
  Cachex.put(:app_prereqs, "bond_deals", bond_deals, ttl: @cache_ttl)

  {bond_deals, bond_mm_deals}
end

defp get_finacle_data(date_string) do
  finnacle_data = MisReports.SourceData.FinnacleTb
    |> where([w], w.report_date == ^date_string and w.ccy_id =="ZMW" and w.sap_gl in ^get_finnacle_sap_gl())
    |> Repo.all()

  # Split into separate lists using Enum.split_with

  Cachex.put(:app_prereqs, "finnacle_data", finnacle_data, ttl: @cache_ttl)
finnacle_data
end

defp get_Tbills_holdings_at_face_value(date_string) do
  case Cachex.get(:app_prereqs, "bond_mm_deals") do
    {:ok, nil} ->
      {_, bond_mm_deals} = get_all_deals_data(date_string)
      sum_face_value(bond_mm_deals)

    {:ok, bond_mm_deals} ->
      sum_face_value(bond_mm_deals)

    _ ->
      Decimal.new("0")
  end
end

defp get_notes_and_coins_value(date_string) do
  case Cachex.get(:app_prereqs, "finnacle_data") do
    {:ok, nil} ->
      finnacle_data = get_finacle_data(date_string)
      sum_coins_value(finnacle_data)

    {:ok, finnacle_data} ->
      sum_coins_value(finnacle_data)

    _ ->
      Decimal.new("0")
  end
end

# Helper function to sum face values
defp sum_face_value(deals) do
  Enum.reduce(deals, Decimal.new("0"), fn deal, total ->
    Decimal.add(total, deal.open_nominal)
  end)
end

defp sum_coins_value(finnacle) do
  Enum.reduce(finnacle, Decimal.new("0"), fn fin, total ->
    Decimal.add(total, fin.lcy_equiv)
  end)
end

defp get_Govt_securities(initial_map) do
  [
    initial_map.govt_bond_ZM1000006370,
    initial_map.govt_bond_ZM1000006388,
    initial_map.govt_bond_ZM1000006396
  ]
  |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
  |> Decimal.abs()
end

defp get_govt_bond_value(date_string, isin) do
  case Cachex.get(:app_prereqs, "bond_deals") do
    {:ok, nil} ->
      {bond_deals, _} = get_all_deals_data(date_string)
      sum_bond_value(bond_deals, isin)

    {:ok, bond_deals} ->
      sum_bond_value(bond_deals, isin)

    _ ->
      Decimal.new("0")
  end
end

# Helper function to sum specific bond values
defp sum_bond_value(deals, isin) do
  deals
  |> Enum.filter(&(&1.isin == isin))
  |> Enum.reduce(Decimal.new("0"), fn deal, total ->
    Decimal.add(total, deal.open_nominal)
  end)
end

# Replace the individual bond functions with a more generic approach
defp get_govt_bond_ZM1000006370(date_string), do: get_govt_bond_value(date_string, "ZM1000006370")
defp get_govt_bond_ZM1000006388(date_string), do: get_govt_bond_value(date_string, "ZM1000006388")
defp get_govt_bond_ZM1000006396(date_string), do: get_govt_bond_value(date_string, "ZM1000006396")


defp get_max_govt_securities() do
   Decimal.new("1387164382.35")
# needs  tbills holdings at face value

end

defp get_minimum_required_CLA(date_string, initial_map) do
   Decimal.mult(
    initial_map.add_kwacha_deposit_liabilities_public,
    Decimal.new("0.06")
  )
   |> Decimal.abs()

end

defp get_minimum_KSR(initial_map) do
   Decimal.mult(
    initial_map.kwacha_deposit_foreign_govt_less_agr,
    Decimal.new("0.26")
  )
   |> Decimal.abs()

end

defp get_minimum_fyc_SR(initial_map) do
   Decimal.mult(
    initial_map.fyc_deposit_foreign_govt_less_agr,
    Decimal.new("0.26")
  )
   |> Decimal.abs()

end

defp get_CLA_ratio(initial_map) do

  if initial_map.add_kwacha_deposit_liabilities_public > Decimal.new(0) do
       Decimal.div(
      initial_map.total_core_liquid,
      initial_map.add_kwacha_deposit_liabilities_public
    )
        |> Decimal.mult(Decimal.new("100"))

     |> Decimal.abs()
  else
    Decimal.new("0")
  end
# needs  tbills holdings at face value

end

defp get_KSR_ratio(initial_map) do

  if initial_map.kwacha_deposit_foreign_govt_less_agr > Decimal.new(0) do
    if initial_map.govt_securities > initial_map.govt_securities do
      Decimal.add(
        initial_map.kwacha_statutory_reserve,
        initial_map.govt_securities
      )
          |> Decimal.mult(Decimal.new("100"))
       |> Decimal.abs()
    else
    Decimal.div(
      initial_map.total_core_liquid,
      initial_map.add_kwacha_deposit_liabilities_public
    )
     |> Decimal.abs()
  end
  else
    Decimal.new("0")

end
end

defp get_excess_shortfall_KSR(initial_map) do

  Decimal.sub(
    if initial_map.govt_securities > initial_map.max_govt_securities  do
      Decimal.add(
        initial_map.kwacha_statutory_reserve,
        initial_map.max_govt_securities
      )
      |> Decimal.abs()
      else
        Decimal.add(
          initial_map.kwacha_statutory_reserve,
          initial_map.govt_securities
        )
        |> Decimal.abs()
    end,
    Decimal.abs(initial_map.minimum_KSR)
  )
# needs  tbills holdings at face value

end

defp get_excess_shortfall_CLA(initial_map) do

 Decimal.sub(
    initial_map.total_core_liquid,
    initial_map.minimum_required_CLA
  )
   |> Decimal.abs()
# needs  tbills holdings at face value

end

defp get_excess_shortfall_fyc_SR(initial_map) do

 Decimal.sub(
    initial_map.fyc_deposit_foreign_govt_less_agr,
    initial_map.minimum_fcy_SR
  )
   |> Decimal.abs()
# needs  tbills holdings at face value

end

defp get_fyc_SR_ratio(initial_map) do

  if initial_map.fyc_deposit_foreign_govt_less_agr > Decimal.new(0) do
    Decimal.div(
      initial_map.fcy_statutory_reserve,
      initial_map.fyc_deposit_foreign_govt_less_agr
    )
    |> Decimal.mult(Decimal.new("100"))
     |> Decimal.abs()
  else
    Decimal.new(0)
  end
# needs  tbills holdings at face value

end

defp get_total_kwacha_deposit_liabilities(date_string) do
  key = {date_string, "ZMW", "27b2"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
end

defp get_total_kwacha_deposit_foreign(date_string) do
  key = {date_string, "ZMW", "27b2"}
  Cachex.get!(:app_prereqs, key)
  |> sum_actual_this_year()
end


def get_ytd_weekly_27(date) do

   # Get weekly BOZ balance data
  weekly_boz_blc_data =
    MisReports.Utilities.WeeklyBozBalance
    |> where([w], w.report_dt == ^date)
    |> Repo.all()
    |> List.first() # Assuming one record per date
  # Initialize the map with BOZ balance data
  initial_map = %{
    col_interbank_loans_made: weekly_boz_blc_data.col_interbank_loans_made || Decimal.new(0),
    col_interbank_loans_received: weekly_boz_blc_data.col_interbank_loans_received || Decimal.new(0),
    current_account_boz: weekly_boz_blc_data.current_account_boz || Decimal.new(0),
    fcy_statutory_reserve: weekly_boz_blc_data.fcy_statutory_reserve || Decimal.new(0),
    kwacha_statutory_reserve: weekly_boz_blc_data.kwacha_statutory_reserve || Decimal.new(0),
    new_fcy_loans_agriculture: weekly_boz_blc_data.new_fcy_loans_agriculture || Decimal.new(0),
    new_kwacha_loans_agriculture: weekly_boz_blc_data.new_kwacha_loans_agriculture || Decimal.new(0),
    omo_repo_placements: weekly_boz_blc_data.omo_repo_placements || Decimal.new(0),
    omo_term_deposits: weekly_boz_blc_data.omo_term_deposits || Decimal.new(0)
  }

  # Start building the map with each calculation
  date_string = Date.to_string(date)
final_map =
initial_map

#get 0 Total Core Liquid Assets (a+b+c+d+e+f-g)
|> Map.put(:total_core_liquid, get_total_core_liquid(date_string, initial_map))
#get 0.01 get Zambia Notes and Coins
|> Map.put(:zambia_notes_coins, get_notes_and_coins_value(date_string))
#get 0.02 Treasury Bill Holdings at face value
|> Map.put(:tbills_holdings_at_face_value, get_Tbills_holdings_at_face_value(date_string))

# get (a) Government bond ISIN ZM1000006370
|> Map.put(:govt_bond_ZM1000006370, get_govt_bond_ZM1000006370(date_string))
# get (b) Government bond ISIN ZM1000006388
|> Map.put(:govt_bond_ZM1000006388, get_govt_bond_ZM1000006388(date_string))
# get (c) Government bond ISIN ZM1000006396
|> Map.put(:govt_bond_ZM1000006396, get_govt_bond_ZM1000006396(date_string))
# get 3.   Government securities (eligible for statutory reserve requirements):
#get 1 Foreign Currency Loans and Advances Outstanding (US $)
# get from 27a3
|> Map.put(:fycla_outstanding, get_fyc_loans_outstanding(date_string))
#get 2 Kwacha Loans and Advances Outstanding
|> Map.put(:kwacha_loans_advances_outstanding, get_kwacha_loans_advances_outstanding(date_string))
#get 3 Kwacha Deposit Liabilities to the Public
|> Map.put(:kwacha_deposit_liabilities_public, get_kwacha_deposit_liabilities_public(date_string))
#get 4 Kwacha Deposit Liabilities to the Public  (a) + (b) = 3 + 0
|> Map.put(:add_kwacha_deposit_liabilities_public, get_add_kwacha_deposit_liabilities_public(date_string))
# ///////////////////////////
|> Map.put(:total_kwacha_deposit_liabilities, get_total_kwacha_deposit_liabilities(date_string))
#get 5 Total Kwacha Deposit Liabilities to Foreign Institutions (Vostro Accounts)
|> Map.put(:total_kwacha_deposit_liabilities_foreign, get_total_kwacha_deposit_foreign(date_string))

#get 5 Total Kwacha Deposit Liabilities to Foreign Institutions (Vostro Accounts)
|> Map.put(:kwacha_deposit_foreign_vostro, get_kwacha_deposit_foreign_vostro(date_string))

#get 6 Total Kwacha Deposit Liabilities to the Government (Govt Deposits)
|> Map.put(:total_kwacha_deposit_liabilities_govt, get_total_kwacha_govt(date_string))

#get 7 Foreign Currency Deposit Liabilities to the Public in US$
|> Map.put(:fyc_deposit_liabilities_public, get_fcy_deposit_liability_public(date_string))

#get 8 Foreign Currency Deposit Liabilities to Foreign Institutions (Vostro Accounts) in US$
|> Map.put(:fcy_deposit_foreign_vostro, get_fcy_deposit_foreign_vostro(date_string))

#get 9 Foreign Currency Deposit Liabilities to Government (Govt Deposits) in US$
|> Map.put(:fcy_deposit_liabilities_govt, get_fcy_deposit_govt(date_string))


#get 11 Kwacha  Deposit Liabilities to the Public, to Foreign Institutions
# and to Government, less New Agricultural  Lending in Kwacha
|> Map.put(:kwacha_deposit_foreign_govt_less_agr, get_kwacha_deposit_foreign_govt_less_agr(date_string))

#get 12 Foreign Currency Deposit Liabilities to the Public, to Foreign Institutions and to Government
#, less New Agricultural Lending in Foreign Currency.
|> Map.put(:fyc_deposit_foreign_govt_less_agr, get_fyc_deposit_foreign_govt_less_agr(date_string))
|> Map.put(:max_govt_securities, get_max_govt_securities())

#get 13 Core Liquid Asset Ratio (% ) ( 1 /6 )
# |> Map.put(:core_liquid_asset_ratio,  fn map -> get_CLA_ratio(initial_map) end)
# #get 14   Minimum Required Core Liquid Assets (6% of 6)
# |> Map.put(:minimum_required_CLA, fn map -> get_minimum_required_CLA(date_string, initial_map)end)
# #get 15   Excess/shortfall in Core Liquid Assets (1 - 15)
# |> Map.put(:excess_shortfall_CLA, fn map -> get_excess_shortfall_CLA(initial_map) end)
# #get 16  Kwacha Statutory Reserve Ratio (% ) ( 2 / 12 )
# |> Map.put(:kwacha_statutory_reserve_ratio, fn map ->  get_KSR_ratio(initial_map)end)
# #get 17 Minimum Kwacha Statutory Reserves (26.0% of 12)
# |> Map.put(:minimum_kwacha_statutory_reserve, fn map ->  get_minimum_KSR(initial_map)end)
# #get 18 Maximum Government securities (eligible for statutory reserve requirements)
# |> Map.put(:govt_securities_max, fn map -> get_govt_securities_max(initial_map)end)
# #get 19 Excess/shortfall in Kwacha Statutory Reserves (2 - 18)
# |> Map.put(:excess_shortfall_KSR, fn map -> get_excess_shortfall_KSR(initial_map)end)
# #get 20 FCY (US $) Statutory Reserve Ratio (% ) ( 3 / 13 )
# |> Map.put(:fcy_statutory_reserve_ratio,fn map ->  get_fyc_SR_ratio(initial_map)end)
# #get 21 Minimum FCY Statutory Reserves (26.0% of 13)
# |> Map.put(:minimum_fyc_statutory_reserve,fn map ->  get_minimum_fyc_SR(initial_map) end)
# #get 22 Excess/shortfall in FCY Statutory Reserves(US $)  (3 - 21)
# |> Map.put(:excess_shortfall_fyc_SR, fn map -> get_excess_shortfall_fyc_SR(initial_map)end)

 |> (fn map -> Map.put(map, :govt_securities, get_Govt_securities(map)) end).()
  |> (fn map -> Map.put(map, :core_liquid_asset_ratio, get_CLA_ratio(map)) end).()
  |> (fn map -> Map.put(map, :minimum_required_CLA, get_minimum_required_CLA(date_string, map)) end).()
  |> (fn map -> Map.put(map, :excess_shortfall_CLA, get_excess_shortfall_CLA(map)) end).()
  |> (fn map -> Map.put(map, :kwacha_statutory_reserve_ratio, get_KSR_ratio(map)) end).()
  |> (fn map -> Map.put(map, :minimum_KSR, get_minimum_KSR(map)) end).()
  |> (fn map -> Map.put(map, :excess_shortfall_KSR, get_excess_shortfall_KSR(map)) end).()
  |> (fn map -> Map.put(map, :fcy_statutory_reserve_ratio, get_fyc_SR_ratio(map)) end).()
  |> (fn map -> Map.put(map, :minimum_fcy_SR, get_minimum_fyc_SR(map)) end).()
  |> (fn map -> Map.put(map, :excess_shortfall_fyc_SR, get_excess_shortfall_fyc_SR(map)) end).()


final_map
end

def get_ytd_weekly(date, currency \\ "", schedule \\ "") do
  with {:ok, _valid_date} <- Date.from_iso8601(date) do

    key = {date, currency, schedule}
    IO.inspect(key, label: "================FOUND My Boy key================")

    case Cachex.get(:app_prereqs, key) do
      {:ok, :nil} ->
        result =
          if schedule == "ALL" do
            # Handle composite schedule "27"
            IO.inspect(schedule, label: "================FOUND My Boy ================")
            get_composite_schedule_27(date, currency)
          else
            # Handle individual sub-schedules
            do_get_ytd_weekly(date, currency, schedule)
          end

        case Cachex.put(:app_prereqs, key, result, ttl: @cache_ttl) do
          {:ok, true} ->
            IO.inspect(key, label: "================MY BOY CREATED THE SMOKE ================")

            if schedule == "ALL" do
                key = {date,"ALL", "27"}

              {:ok, result} = Cachex.get(:app_prereqs, key)
              IO.inspect(key, label: "================ RETURNING MODIFIED RESULT ================")

                result
            else
              result
              end


          {:error, reason} ->
            IO.puts("Failed to cache result: #{inspect(reason)}")
            result
        end

      {:ok, cached_result} ->
         if schedule == "ALL" do
          key = {date,"ALL", "27"}

         {:ok, cached_result} = Cachex.get(:app_prereqs, key)
          IO.inspect(key, label: "================ RETURNING MODIFIED RESULT ================")

          cached_result
        else
        cached_result
         end

      {:error, reason} ->
        IO.puts("Error fetching from cache: #{inspect(reason)}")
        if schedule == "27" do
          get_composite_schedule_27(date, currency)
        else
          do_get_ytd_weekly(date, currency, schedule)
        end
    end
  else
    {:error, reason} ->
      {:error, :invalid_date}
  end
end


defp do_get_ytd_weekly(date, currency, schedule) do
  IO.inspect(schedule, label: "================Getting for ================")

  gls = get_gls_for_schedule(schedule)
  optional_accounts = get_optional_accounts(schedule,[])
  Cachex.put(:app_prereqs, "optional_accounts", optional_accounts, ttl: @cache_ttl)
  cur_date = Date.from_iso8601!(date)
  prev_date = Timex.shift(cur_date, weeks: -1)
  cur_usd_rate = Utilities.get_exchange_rate_by_date_and_code(cur_date, "USD")[:exchange_rate_lcy] || "1"
   Cachex.put(:app_prereqs, "cur_usd_rate", cur_usd_rate, ttl: @cache_ttl)
  prev_usd_rate = Utilities.get_exchange_rate_by_date_and_code(prev_date, "USD")[:exchange_rate_lcy] || "1"

  # Early return for schedule 27
  if schedule == "27" do
    IO.puts("================ Processing Schedule 27 ================")
    get_ytd_weekly_27(cur_date)
  else
    IO.puts("================ Processing Sub Module ================")
    {current_entries, previous_entries} = case currency do
      "" ->
        {[], []}
      _ ->
        # Build base query for current entries
        current_query = WeeklyReportEntriesCustContribution
          |> join(:left, [a], g in "gmo_tpins", on: a.customer_number_local_cif == g.customer_number)
          |> where([a, _g], a.date == ^cur_date)
          |> where([a, _g], not is_nil(a.date))
          |> then(fn q ->
            if optional_accounts != [] do
              where(q, [a, _g], a.account_number in ^optional_accounts)
            else
              q
            end
          end)
          |> then(fn q ->
            if gls != [] do
              where(q, [a, _g], a.balance_sap_ledger_no in ^gls)
            else
              q
            end
          end)
          |> apply_source_system_filters(schedule)
          |> apply_currency_filters(schedule, currency)
          |> select([a, g], %{
            acc_no: a.account_number,
            source_system: a.source_system,
            bal_num: a.balance_sap_ledger_no,
            acc_name: a.account_name,
            actual_credit_balance: a.actual_credit_balance,
            actual_debit_balance: a.actual_debit_balance,
            currency_code: a.currency_code,
            effective_debit_rate: a.effective_debit_rate,
            tpin: g.tax_identification_number
          })
          # |> limit(5)



        # Get previous entries based on date
        previous_query = case {cur_date.month, cur_date.day} do
          {1, 1} ->
            []
          _ ->
            WeeklyReportEntriesCustContribution
              |> join(:left, [a], g in "gmo_tpins", on: a.customer_number_local_cif == g.customer_number)
              |> where([a, _g], a.date == ^prev_date)
              |> where([a, _g], not is_nil(a.date))
              |> then(fn q ->
                if optional_accounts != [] do
                  where(q, [a, _g], a.account_number in ^optional_accounts)
                else
                  q
                end
              end)
              |> then(fn q ->
                if gls != [] do
                  where(q, [a, _g], a.balance_sap_ledger_no in ^gls)
                else
                  q
                end
              end)
              |> apply_source_system_filters(schedule)
              |> apply_currency_filters(schedule, currency)
              |> select([a, g], %{
                acc_no: a.account_number,
                source_system: a.source_system,
                bal_num: a.balance_sap_ledger_no,
                acc_name: a.account_name,
                actual_credit_balance: a.actual_credit_balance,
                actual_debit_balance: a.actual_debit_balance,
                currency_code: a.currency_code,
                effective_debit_rate: a.effective_debit_rate,
                tpin: g.tax_identification_number
              })
              # |> limit(5)

        end

     current_entries  = Repo.all(current_query)
      previous_entries = Repo.all(previous_query)



        {current_entries, previous_entries}
    end

    # Process entries into maps
    previous_entries_map = Map.new(previous_entries, fn entry -> {entry.acc_no, entry} end)
    current_entries_map = Map.new(current_entries, fn entry -> {entry.acc_no, entry} end)

    # Get all account numbers
    all_acc_nos = previous_entries
      |> Enum.map(& &1.acc_no)
      |> Enum.concat(Enum.map(current_entries, & &1.acc_no))
      |> Enum.uniq()

    IO.inspect(schedule, label: "================Getting for schedule ================")

    # Process submodule data
    get_submodule_data(
      all_acc_nos,
      previous_entries_map,
      current_entries_map,
      schedule,
      prev_usd_rate,
      cur_usd_rate
    )
  end
end

defp get_submodule_data(all_acc_nos, previous_entries_map,current_entries_map,schedule, prev_usd_rate, cur_usd_rate) do

  Enum.map(all_acc_nos, fn acc_no ->
    previous_entry =
      Map.get(previous_entries_map, acc_no, %{
        acc_no: acc_no,
        actual_credit_balance: Decimal.new(0),
        actual_debit_balance: Decimal.new(0)
      })

    current_entry =
      Map.get(current_entries_map, acc_no, %{
        acc_no: acc_no,
        actual_credit_balance: Decimal.new(0),
        actual_debit_balance: Decimal.new(0)
      })

    {prev_actual_this_year, current_actual_this_year} =
      case schedule do
        s when s in ["27a1", "27a2", "27b1", "27b2", "27a5"] ->
          get_balance_values(previous_entry, current_entry, :credit)

        s when s in ["27a3", "27a4"] ->
          get_balance_values(previous_entry, current_entry, :debit)

        _ ->
          {Decimal.new(0), Decimal.new(0)}
      end

    actual_this_year_difference =
      case schedule do
        s when s in ["27a1", "27a3", "27a1"] ->
          Decimal.sub(current_actual_this_year, prev_actual_this_year)

        s when s in ["27a2", "27b2", "27a4"] ->
          usd_cur = convert(current_actual_this_year, cur_usd_rate) |> Decimal.round(2)
          usd_prev = convert(prev_actual_this_year, prev_usd_rate) |> Decimal.round(2)
          Decimal.sub(usd_cur, usd_prev)

        _ ->
          Decimal.sub(current_actual_this_year, prev_actual_this_year)
      end

    entry =
      case Map.has_key?(current_entry, :bal_num) do
        false -> previous_entry
        _ -> current_entry
      end

    current_actual_this_year =
      case schedule do
        s when s in ["27b2"] ->
          convert(current_actual_this_year, cur_usd_rate) |> Decimal.round(2)

        s when s in ["27a5"] and entry.currency_code == "USD" ->
          convert(current_actual_this_year, cur_usd_rate) |> Decimal.round(2)

        _ ->
          current_actual_this_year
      end

    activity =
      case schedule do
        "27a1" -> gen_activity_data_27a1(prev_actual_this_year, current_actual_this_year)
        "27a2" -> gen_activity_data_27a1(prev_actual_this_year, current_actual_this_year)
        "27a3" -> gen_activity_data_27a3(prev_actual_this_year, current_actual_this_year)
        "27a4" -> gen_activity_data_27a3(prev_actual_this_year, current_actual_this_year)
        _ -> []
      end

    %{
      acc_no: entry.acc_no,
      bal_leg_num: entry.bal_num,
      acc_name: entry.acc_name,
      tpin: entry.tpin,
      currency_code: entry.currency_code,
      effective_debit_rate: entry.effective_debit_rate,
      actual_this_year: current_actual_this_year,
      prev_actual_this_year: prev_actual_this_year,
      actual_this_year_difference: Decimal.abs(actual_this_year_difference),
      activity_type: activity
    }
  end)
end

defp get_composite_schedule_27(date, currency) do
  sub_schedules = ["27a1,ZMW", "27a2,ZMW", "27a3,ZMW", "27a4,ZMW", "27a5,ALL", "27b1,ZMW", "27b2,ZMW","27,ALL"]
  Enum.flat_map(sub_schedules, fn sub_schedule ->
    schedule = String.split(sub_schedule, ",") |> List.first()
    currency = String.split(sub_schedule, ",") |> List.last()
    get_ytd_weekly(date, currency, schedule)
  end)
end


defp get_gls_for_schedule(schedule) do
  case schedule do
    "27a1" -> get_bal_sap_ledger_no_27a1()
    "27a2" -> get_bal_sap_ledger_no_27a1()
    "27a3" -> get_bal_sap_ledger_no_27a3()
    "27a4" -> get_bal_sap_ledger_no_27a3()
    _ -> []
  end
end

defp get_optional_accounts(schedule, optional_accounts) do
  case schedule do
    "27a5" -> get_acc_nso_27a5()
    "27b1" -> list_govt_accounts() |> Enum.map(& &1.account_number)
    "27b2" -> list_govt_accounts() |> Enum.map(& &1.account_number)
    _ -> optional_accounts
  end
end

defp apply_source_system_filters(query, schedule) do
  case schedule do
    s when s in ["27a3", "27a4"] ->
      where(query, [a, _g], not like(a.source_system, "AE Financial Journals"))
    _ ->
      query
  end
end

defp apply_currency_filters(query, schedule, currency) do
  case schedule do
    s when s in ["27a2", "27b2", "27a4"] ->
      where(query, [a, _g], a.currency_code != ^currency)
    "27a5" ->
      query
    _ ->
      where(query, [a, _g], a.currency_code == ^currency)
  end
end

defp get_balance_values(previous_entry, current_entry, :credit) do
  {Map.get(previous_entry, :actual_credit_balance, Decimal.new(0)),
   Map.get(current_entry, :actual_credit_balance, Decimal.new(0))}
end

defp get_balance_values(previous_entry, current_entry, :debit) do
  {Map.get(previous_entry, :actual_debit_balance, Decimal.new(0)),
   Map.get(current_entry, :actual_debit_balance, Decimal.new(0))}
end

defp gen_activity_data_27a1(prev_actual_this_year, actual_this_year) do
  case Decimal.compare(Decimal.abs(prev_actual_this_year), Decimal.abs(actual_this_year)) do
    :gt -> "Withdrawal"
    _ -> "Deposit"
  end
end

defp gen_activity_data_27a3(prev_actual_this_year, actual_this_year) do
  case Decimal.compare(Decimal.abs(prev_actual_this_year), Decimal.abs(actual_this_year)) do
    :gt -> "Repayment"
    _ -> "Advance"
  end
end

defp get_bal_sap_ledger_no_27a1() do
  [
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "0000249900",
    "**********"
  ]
end

defp get_bal_sap_ledger_no_27a3() do
  [
    "0000060225",
    "0000061450",
    "0000061950",
    "0000062150",
    "0000065500",
    "0000070005",
    "0000070500",
    "0000070850",
    "0000071150",
    "0000072650",
    "0000072750",
    "0000073050",
    "0000073250",
    "0000073850",
    "0000076905",
    "0000076920",
    "0000080500",
    "0000085255",
    "0*********",
    "**********",
    "**********",
    "**********",
    "**********"
  ]
end

defp get_acc_nso_27a5() do
  [
    "9130000459910",
    "9130001124440",
    "9130006613849",
    "9130002449335",
    "9130000204696",
    "9130002354121",
    "9130002420558",
    "9130000014757",
    "9130005334717",
    "9130000018884",
    "9130002087840",
    "9130001175169",
    "9130001712334",
    "9130001887762",
    "9130001985993",
    "9130001893835",
    "9130001893878",
    "9130001945657",
    "9130001945126",
    "9130002455254",
    "9130002455661",
    "9130003216725",
    "9130002818187",
    "9130003353560",
    "9130001245299",
    "9130001712520",
    "9130001768356",
    "9130006848374",
    "9130006311249",
    "9130005555888",
    "9130005555810",
    "9130005555845",
    "9130005117902",
    "9130002467457",
    "9130003400194",
    "9130003377249",
    "9130000277006",
    "9130000533975",
    "9130001151383",
    "9130000717650",
    "9130002630269",
    "9130002362949",
    "9130000424467",
    "9130000238531",
    "9130000816640",
    "9130003270177",
    "9130003271637",
    "9130000027476",
    "9130002265307",
    "9130002524264",
    "9130000114050",
    "9130001985829",
    "9130004407451",
    "9130003372255",
    "9130003373359",
    "9130002779688",
    "9130003387619",
    "9130003351363",
    "9130001500272",
    "9130002375374",
    "9130002456390",
    "9130002456471",
    "9130002456579",
    "9130002456609",
    "9130002456668",
    "9130005155375",
    "28525795",
    "29644361",
    "30550316",
    "30522219",
    "28525770",
    "29250232",
    "31128790",
    "31340577",
    "34576892",
    "28525768",
    "28525753",
    "30550327",
    "30550319",
    "31454826",
    "31240337",
    "29534360",
    "28525714",
    "34988015",
    "30854297",
    "30953420",
    "34184892",
    "33762068",
    "34419748",
    "34123102",
    "31069105",
    "34465555",
    "34147882",
    "33247962",
    "34946191",
    "34000250",
    "28757029",
    "33606456",
    "30511513",
    "30870586",
    "30701387",
    "30598779",
    "29467336",
    "30598765",
    "30619433",
    "29523253",
    "30510653",
    "131000AUD0117036",
    "30598766",
    "33907968",
    "30740228",
    "30754620",
    "29243179",
    "30718099",
    "30847322",
    "30550336",
    "30900429",
    "29037016",
    "28535510",
    "30550379",
    "29574307",
    "29467388",
    "131000KES0117091"
  ]
end

# Helper functions for cache management
def warm_cache(dates) do
  Task.async_stream(dates, fn date ->
    get_ytd_weekly(date, "", "")
  end)
  |> Stream.run()
end

def invalidate_cache(date) do
  Cachex.del(:app_prereqs, {date, "", ""})
end

def clear_cache do
  Cachex.clear(:app_prereqs)
end

def convert(amount, usd_rate) do
  Decimal.div(amount, usd_rate)
end

def convert_and_update(data, usd_rate) do
  Enum.map(data, fn item ->
    updated_amount =
      item[:actual_credit_balance]
      |> Decimal.div(usd_rate)
      |> Decimal.round(2)

    Map.put(item, :actual_credit_balance, updated_amount)
  end)
end

  def get_ytd_weekly_test(date) do
    gls = get_bal_sap_ledger_no_27a1()
    cur_date = Date.from_iso8601!(date)

    WeeklyReportEntriesCustContribution
    |> where(
      [a],
      a.date == ^cur_date and
        a.currency_code == "ZMW" and
        a.balance_sap_ledger_no in ^gls and
        not is_nil(a.actual_credit_balance) and
        a.actual_credit_balance != 0
    )
    |> join(:inner, [a], b in "gmo_tpins", on: a.customer_number_global_cif == b.customer_number)
    |> select([a, b], %{
      acc_no: a.account_number,
      acc_name: a.account_name,
      amount: a.actual_credit_balance,
      tpin: b.tax_identification_number,
      bal_leg_num: a.balance_sap_ledger_no,
      activity_type: ""
    })
    |> distinct(true)
    |> Repo.all()
  end

  # def get_ytd_weekly(from, to) do
  #   _year = String.slice(from, 0..3)
  #   _month = String.slice(from, 5..6)

  #   cur_date = Date.from_iso8601!(from) |> Timex.end_of_month()
  #   prev_date = Timex.shift(cur_date, months: -1) |> Timex.end_of_month()

  #   current_entries =
  #     TrialBalance
  #     |> where([a], a.date == ^cur_date and not is_nil(a.actual_this_year))
  #     |> select([a], %{
  #       gl_no: a.gl_no,
  #       actual_this_year: a.actual_this_year,
  #       date: a.date
  #     })
  #     |> Repo.all()

  #   previous_entries =
  #     case Timex.month_name(cur_date.month) do
  #       "January" ->
  #         []
  #       _ ->
  #         TrialBalance
  #         |> where([a], a.date == ^prev_date and not is_nil(a.actual_this_year))
  #         |> select([a], %{
  #           gl_no: a.gl_no,
  #           actual_this_year: a.actual_this_year,
  #           date: a.date
  #         })
  #         |> Repo.all()
  #     end

  #   # Create a map for the previous entries using gl_no as the key
  #   previous_entries_map = Map.new(previous_entries, fn entry -> {entry.gl_no, entry} end)

  #   Enum.map(current_entries, fn current_entry ->
  #     previous_entry = Map.get(previous_entries_map, current_entry.gl_no, %{})
  #     prev_actual_this_year = Map.get(previous_entry, :actual_this_year, Decimal.new(0))
  #     current_actual_this_year = Map.get(current_entry, :actual_this_year, Decimal.new(0))

  #     actual_this_year_difference = Decimal.sub(current_actual_this_year, prev_actual_this_year)

  #     %{
  #       gl_no: current_entry.gl_no,
  #       actual_this_year: current_actual_this_year,
  #       prev_actual_this_year: prev_actual_this_year,
  #       actual_this_year_difference: actual_this_year_difference
  #     }
  #   end)
  # end

  def ledger_numbers() do
    [
      "**********", "**********", "**********", "**********",
      "**********", "**********", "**********"
    ]
  end

  def get_weekly_report_entries_with_ratings(date) do
    {:ok, date} = Date.from_iso8601(date)

    last_week_start = Timex.beginning_of_week(Timex.shift(date, weeks: -1), :monday)
    last_week_end = Timex.end_of_week(Timex.shift(date, weeks: -1), :saturday)

    query =
      WeeklyReportEntriesCustContribution
      |> where([w], w.balance_sap_ledger_no in ^ledger_numbers() and w.date >= ^last_week_start and w.date <= ^last_week_end)
      |> join(:left, [w], e in MisReports.Utilities.ExchangePlacement, on: w.account_name == e.institution_name)
      |> group_by([w, e], [fragment("CASE
        WHEN ? IN ('Bank 04 Fx Curr Intercompany Acct-06', 'Bank 04 Fx Curr Intercompany Acct-07', 'Standard Bank Of South Africa Intercompany',
                  'Standard Bank Of South Africa Ltd', 'Standard Bank South Africa Dkk Intercompany',
                  'Standard Bank South Africa Nok Intercompany', 'Standard Bank South Africa Zar Intercompany',
                  'Standard Bank Southafrica -Cny Intercompany') THEN 'Standard Bank South Africa'
        WHEN ? IN ('Icbc Cny Reserve', 'Icbc Standard Bank Cny', 'Icbc Standard Bank Plc') THEN 'ICBC'
        WHEN ? IN ('Unicredit Bank Ag', 'Unicredit Bank Gmbh') THEN 'Unicredit Bank Ag'
        ELSE ? END", w.account_name, w.account_name, w.account_name, w.account_name), w.date, e.rating, e.rating_agency, e.maximum])
      |> select([w, e], %{
        account_name: fragment("CASE
          WHEN ? IN ('Bank 04 Fx Curr Intercompany Acct-06', 'Bank 04 Fx Curr Intercompany Acct-07', 'Standard Bank Of South Africa Intercompany', 'Standard Bank Of South Africa Ltd', 'Standard Bank South Africa Dkk Intercompany', 'Standard Bank South Africa Nok Intercompany', 'Standard Bank South Africa Zar Intercompany', 'Standard Bank Southafrica -Cny Intercompany') THEN 'Standard Bank South Africa'
          WHEN ? IN ('Icbc Cny Reserve', 'Icbc Standard Bank Cny', 'Icbc Standard Bank Plc') THEN 'ICBC'
          WHEN ? IN ('Unicredit Bank Ag', 'Unicredit Bank Gmbh') THEN 'Unicredit Bank Ag'
          ELSE ? END", w.account_name, w.account_name, w.account_name, w.account_name),
        date: w.date,
        actual_debit_balance: sum(w.actual_debit_balance),
        rating: e.rating,
        rating_agency: e.rating_agency,
        maximum: e.maximum
      })

    results = Repo.all(query)

    weekday_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

    # Group results by day and format them as desired
    results
    |> Enum.group_by(& &1.date)
    |> Enum.map(fn {day, entries} ->
      weekday_name = Enum.at(weekday_names, Date.day_of_week(day) - 1)

      %{day: weekday_name, entries: entries}
    end)
  end

  alias MisReports.SourceData.GovtAccounts

  @doc """
  Returns the list of govt_accounts.

  ## Examples

      iex> list_govt_accounts()
      [%GovtAccounts{}, ...]

  """
  def list_govt_accounts do
    Repo.all(GovtAccounts)
  end

  def list_govt_accounts(params) do
    GovtAccounts
    |> preload([:checker, :maker])
    |> govt_accounts_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp govt_accounts_isearch_filter(query, nil), do: query

  defp govt_accounts_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.account_name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.account_number, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.product_code_source, ^search_term))
  end

  @doc """
  Gets a single govt_accounts.

  Raises `Ecto.NoResultsError` if the Govt accounts does not exist.

  ## Examples

      iex> get_govt_accounts!(123)
      %GovtAccounts{}

      iex> get_govt_accounts!(456)
      ** (Ecto.NoResultsError)

  """
  def get_govt_accounts!(id), do: Repo.get!(GovtAccounts, id)

  @doc """
  Creates a govt_accounts.

  ## Examples

      iex> create_govt_accounts(%{field: value})
      {:ok, %GovtAccounts{}}

      iex> create_govt_accounts(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_govt_accounts(attrs \\ %{}) do
    %GovtAccounts{}
    |> GovtAccounts.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a govt_accounts.

  ## Examples

      iex> update_govt_accounts(govt_accounts, %{field: new_value})
      {:ok, %GovtAccounts{}}

      iex> update_govt_accounts(govt_accounts, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_govt_accounts(%GovtAccounts{} = govt_accounts, attrs) do
    govt_accounts
    |> GovtAccounts.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a govt_accounts.

  ## Examples

      iex> delete_govt_accounts(govt_accounts)
      {:ok, %GovtAccounts{}}

      iex> delete_govt_accounts(govt_accounts)
      {:error, %Ecto.Changeset{}}

  """
  def delete_govt_accounts(%GovtAccounts{} = govt_accounts) do
    Repo.delete(govt_accounts)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking govt_accounts changes.

  ## Examples

      iex> change_govt_accounts(govt_accounts)
      %Ecto.Changeset{data: %GovtAccounts{}}

  """
  def change_govt_accounts(%GovtAccounts{} = govt_accounts, attrs \\ %{}) do
    GovtAccounts.changeset(govt_accounts, attrs)
  end

  alias MisReports.SourceData.DailyGut

  @doc """
  Returns the list of daily_gut.

  ## Examples

      iex> list_daily_gut()
      [%DailyGut{}, ...]

  """
  def list_daily_gut do
    Repo.all(DailyGut)
  end

  def gbm_by_day(month) do
    DailyGut
    |> where(month: ^month)
    |> Repo.all()
  end

  @doc """
  Returns a list of dates for the previous week's business days (Monday-Friday) from the given date.

  ## Examples

      iex> get_previous_week_business_days("2024-01-13")
      ["2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12"]

  """

  def get_previous_week_entries(date) do
    {:ok, date} = Date.from_iso8601(date)

    last_week_start = Timex.beginning_of_week(Timex.shift(date, weeks: -1), :monday)
    last_week_end = Timex.end_of_week(Timex.shift(date, weeks: -1), :friday)

    query =
      from dg in DailyGut,
        where: dg.date >= ^last_week_start and dg.date <= ^last_week_end,
        select: %{
          day: dg.date,
          legal_entity: dg.legal_entity,
          year: dg.year,
          month: dg.month,
          sap_gl_acc_no: dg.sap_gl_acc_no,
          ccy_code: dg.ccy_code,
          ccy_cat: dg.ccy_cat,
          acc_bal_in_lcy: dg.acc_bal_in_lcy,
          acc_bal_in_ccy: dg.acc_bal_in_ccy,
          avg_bal_in_lcy: dg.avg_bal_in_lcy,
          avg_bal_in_ccy: dg.avg_bal_in_ccy,

        }

    results = Repo.all(query)

    weekday_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

    # Group results by day and format them as desired
    results
    |> Enum.group_by(& &1.day)
    |> Enum.map(fn {day, entries} ->
      weekday_name = Enum.at(weekday_names, Date.day_of_week(day) - 1)

      %{day: weekday_name, entries: entries}
    end)
  end

  @doc """
  Gets a single daily_gut.

  Raises `Ecto.NoResultsError` if the Daily gut does not exist.

  ## Examples

      iex> get_daily_gut!(123)
      %DailyGut{}

      iex> get_daily_gut!(456)
      ** (Ecto.NoResultsError)

  """
  def get_daily_gut!(id), do: Repo.get!(DailyGut, id)

  @doc """
  Creates a daily_gut.

  ## Examples

      iex> create_daily_gut(%{field: value})
      {:ok, %DailyGut{}}

      iex> create_daily_gut(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_daily_gut(attrs \\ %{}) do
    %DailyGut{}
    |> DailyGut.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a daily_gut.

  ## Examples

      iex> update_daily_gut(daily_gut, %{field: new_value})
      {:ok, %DailyGut{}}

      iex> update_daily_gut(daily_gut, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_daily_gut(%DailyGut{} = daily_gut, attrs) do
    daily_gut
    |> DailyGut.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a daily_gut.

  ## Examples

      iex> delete_daily_gut(daily_gut)
      {:ok, %DailyGut{}}

      iex> delete_daily_gut(daily_gut)
      {:error, %Ecto.Changeset{}}

  """
  def delete_daily_gut(%DailyGut{} = daily_gut) do
    Repo.delete(daily_gut)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking daily_gut changes.

  ## Examples

      iex> change_daily_gut(daily_gut)
      %Ecto.Changeset{data: %DailyGut{}}

  """
  def change_daily_gut(%DailyGut{} = daily_gut, attrs \\ %{}) do
    DailyGut.changeset(daily_gut, attrs)
  end

  alias MisReports.SourceData.DailyAllDeal

  @doc """
  Returns the list of daily_all_deal_entries.

  ## Examples

      iex> list_daily_all_deal_entries()
      [%DailyAllDeal{}, ...]

  """
  def list_daily_all_deal_entries do
    Repo.all(DailyAllDeal)
  end

  @doc """
  Gets a single daily_all_deal.

  Raises `Ecto.NoResultsError` if the Daily all deal does not exist.

  ## Examples

      iex> get_daily_all_deal!(123)
      %DailyAllDeal{}

      iex> get_daily_all_deal!(456)
      ** (Ecto.NoResultsError)

  """
  def get_daily_all_deal!(id), do: Repo.get!(DailyAllDeal, id)

  @doc """
  Creates a daily_all_deal.

  ## Examples

      iex> create_daily_all_deal(%{field: value})
      {:ok, %DailyAllDeal{}}

      iex> create_daily_all_deal(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_daily_all_deal(attrs \\ %{}) do
    %DailyAllDeal{}
    |> DailyAllDeal.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a daily_all_deal.

  ## Examples

      iex> update_daily_all_deal(daily_all_deal, %{field: new_value})
      {:ok, %DailyAllDeal{}}

      iex> update_daily_all_deal(daily_all_deal, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_daily_all_deal(%DailyAllDeal{} = daily_all_deal, attrs) do
    daily_all_deal
    |> DailyAllDeal.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a daily_all_deal.

  ## Examples

      iex> delete_daily_all_deal(daily_all_deal)
      {:ok, %DailyAllDeal{}}

      iex> delete_daily_all_deal(daily_all_deal)
      {:error, %Ecto.Changeset{}}

  """
  def delete_daily_all_deal(%DailyAllDeal{} = daily_all_deal) do
    Repo.delete(daily_all_deal)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking daily_all_deal changes.

  ## Examples

      iex> change_daily_all_deal(daily_all_deal)
      %Ecto.Changeset{data: %DailyAllDeal{}}

  """
  def change_daily_all_deal(%DailyAllDeal{} = daily_all_deal, attrs \\ %{}) do
    DailyAllDeal.changeset(daily_all_deal, attrs)
  end

  alias MisReports.SourceData.NostroAccounts

  @doc """
  Returns the list of nostro_accounts.

  ## Examples

      iex> list_nostro_accounts()
      [%NostroAccounts{}, ...]

  """
  def list_nostro_accounts do
    Repo.all(NostroAccounts)
  end

  account_numbers = [
    "131000ZAR0105008", "131000ZAR0105009", "131000BWP0117011", "131000KES0117091",
    "131000JPY0112081", "131000INR0112192", "131000SZL0117143", "131000MWK0117111",
    "131000TZS0117092", "131000USD0117152", "131000ZAR0107000", "131000CNY0112193",
    "131000MWK0112111", "131000INR0112197", "131000USD0112153", "131000GBP0112063",
    "131000CAD0112022", "131000SEK0112141", "131000SEK0112142", "131000NZD0112131",
    "131000EUR0112051", "131000AUD0112001", "131000GBP0112061", "131000USD0112154"
  ]

  def get_nostro_accounts_by_account_numbers(account_numbers) do
    NostroAccounts
    |> where([n], n.account_number in ^account_numbers)
    |> select([n], %{account_name: n.account_name, account_number: n.account_number})
    |> Repo.all()
  end

  @doc """
  Gets a single nostro_accounts.

  Raises `Ecto.NoResultsError` if the Nostro accounts does not exist.

  ## Examples

      iex> get_nostro_accounts!(123)
      %NostroAccounts{}

      iex> get_nostro_accounts!(456)
      ** (Ecto.NoResultsError)

  """
  def get_nostro_accounts!(id), do: Repo.get!(NostroAccounts, id)

  @doc """
  Creates a nostro_accounts.

  ## Examples

      iex> create_nostro_accounts(%{field: value})
      {:ok, %NostroAccounts{}}

      iex> create_nostro_accounts(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_nostro_accounts(attrs \\ %{}) do
    %NostroAccounts{}
    |> NostroAccounts.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a nostro_accounts.

  ## Examples

      iex> update_nostro_accounts(nostro_accounts, %{field: new_value})
      {:ok, %NostroAccounts{}}

      iex> update_nostro_accounts(nostro_accounts, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_nostro_accounts(%NostroAccounts{} = nostro_accounts, attrs) do
    nostro_accounts
    |> NostroAccounts.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a nostro_accounts.

  ## Examples

      iex> delete_nostro_accounts(nostro_accounts)
      {:ok, %NostroAccounts{}}

      iex> delete_nostro_accounts(nostro_accounts)
      {:error, %Ecto.Changeset{}}

  """
  def delete_nostro_accounts(%NostroAccounts{} = nostro_accounts) do
    Repo.delete(nostro_accounts)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking nostro_accounts changes.

  ## Examples

      iex> change_nostro_accounts(nostro_accounts)
      %Ecto.Changeset{data: %NostroAccounts{}}

  """
  def change_nostro_accounts(%NostroAccounts{} = nostro_accounts, attrs \\ %{}) do
    NostroAccounts.changeset(nostro_accounts, attrs)
  end

  alias MisReports.SourceData.CounterpartySecurities

  @doc """
  Returns the list of tbl_counterparty_securities.

  ## Examples

      iex> list_tbl_counterparty_securities()
      [%CounterpartySecurities{}, ...]

  """
  def list_tbl_counterparty_securities do
    Repo.all(CounterpartySecurities)
  end

  def get_counter_party_securities(date) do
    CounterpartySecurities
    |> where([a], a.report_date == ^date and a.status == "A")
    |> select(
      [a],
      %{
        issuer_name: a.issuer_name,
        security_type: a.security_type,
        foreign_and_other_debt_securities: a.foreign_and_other_debt_securities
      }
    )
    |> Repo.all()
  end

  def list_counterparty_securities(params) do
    CounterpartySecurities
    |> preload([:checker, :maker])
    |> counterparty_securities_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp counterparty_securities_isearch_filter(query, nil), do: query

  defp counterparty_securities_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.security_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.issuer_name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  end


  @doc """
  Gets a single counterparty_securities.

  Raises `Ecto.NoResultsError` if the Counterparty securities does not exist.

  ## Examples

      iex> get_counterparty_securities!(123)
      %CounterpartySecurities{}

      iex> get_counterparty_securities!(456)
      ** (Ecto.NoResultsError)

  """
  def get_counterparty_securities!(id), do: Repo.get!(CounterpartySecurities, id)

  @doc """
  Creates a counterparty_securities.

  ## Examples

      iex> create_counterparty_securities(%{field: value})
      {:ok, %CounterpartySecurities{}}

      iex> create_counterparty_securities(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_counterparty_securities(attrs \\ %{}) do
    %CounterpartySecurities{}
    |> CounterpartySecurities.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a counterparty_securities.

  ## Examples

      iex> update_counterparty_securities(counterparty_securities, %{field: new_value})
      {:ok, %CounterpartySecurities{}}

      iex> update_counterparty_securities(counterparty_securities, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_counterparty_securities(%CounterpartySecurities{} = counterparty_securities, attrs) do
    counterparty_securities
    |> CounterpartySecurities.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a counterparty_securities.

  ## Examples

      iex> delete_counterparty_securities(counterparty_securities)
      {:ok, %CounterpartySecurities{}}

      iex> delete_counterparty_securities(counterparty_securities)
      {:error, %Ecto.Changeset{}}

  """
  def delete_counterparty_securities(%CounterpartySecurities{} = counterparty_securities) do
    Repo.delete(counterparty_securities)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking counterparty_securities changes.

  ## Examples

      iex> change_counterparty_securities(counterparty_securities)
      %Ecto.Changeset{data: %CounterpartySecurities{}}

  """
  def change_counterparty_securities(%CounterpartySecurities{} = counterparty_securities, attrs \\ %{}) do
    CounterpartySecurities.changeset(counterparty_securities, attrs)
  end


  alias MisReports.SourceData.DailyAllDeal
  alias MisReports.SourceData.AllDeal
  alias MisReports.SourceData.NostroAccounts
  alias MisReports.Utilities.ExchangePlacement

  def get_combined_results do
    deals = get_all_deals()
    accounts = get_account_numbers_and_names()
    placements = get_all_exchange_placements()

    # Build maps for quick lookup using account_name as key.
    deals_map = Map.new(deals, fn deal -> {deal.account_name, deal} end)
    accounts_map = Map.new(accounts, fn acc -> {acc.account_name, acc} end)
    placements_map = Map.new(placements, fn place -> {place.account_name, place} end)

    # Get a list of all unique account names from all sources.
    all_account_names =
      Map.keys(deals_map)
      |> Kernel.++(Map.keys(accounts_map))
      |> Kernel.++(Map.keys(placements_map))
      |> Enum.uniq()

    # Build the final combined result for each account name.
    Enum.map(all_account_names, fn account_name ->
      deal = Map.get(deals_map, account_name, %{})
      account = Map.get(accounts_map, account_name, %{})
      placement = Map.get(placements_map, account_name, %{})

      # Choose report_date: prioritize deals, then accounts, then placements.
      report_date =
        deal[:report_date] ||
        account[:report_date] ||
        (placement && placement[:entry] && placement[:entry][:report_date])

      %{
        account_name: account_name,
        clean_setlemt_amt: deal[:clean_setlemt_amt],
        account_numbers: account[:account_numbers],
        exchange_placement: placement && placement[:entry],
        report_date: report_date
      }
    end)
  end



  def get_all_deals do
    DailyAllDeal
    |> where([d],
      d.prod_cate == "DEPOSIT" and
        d.prod_type == "CASH" and
        d.counter_party_grp != "INTER-DIVISIONAL" and
       d.trade_ccy != "ZMW"
    )
    |> select([d], %{
      prod_cate: d.prod_cate,
      prod_type: d.prod_type,
      trade_ccy: d.trade_ccy,
      account_name: d.counter_party,
      counter_party_grp: d.counter_party_grp,
      clean_setlemt_amt: d.clean_setlemt_amt,
      report_date: d.date
    })
    |> Repo.all()
    |> Enum.map(fn deal ->
      normalized_account_name =
        if String.contains?(deal.account_name, "SB SOUTH AFRICA") or
             String.contains?(deal.account_name, "STANDARD BANK SA") or
             String.contains?(deal.account_name, "STANDARD BANK SOUTHAFRICA") or
             String.contains?(deal.account_name, "STANDARD BANK SOUTH AFRICA") do
          "Standard Bank South Africa"
        else
          deal.account_name
        end

      %{deal | account_name: normalized_account_name}
    end)
    |> Enum.group_by(& &1.account_name)
    |> Enum.map(fn {account_name, deals} ->
      total_clean_setlemt_amt =
        deals
        |> Enum.reduce(Decimal.new("0"), fn deal, acc ->
          Decimal.add(acc, deal.clean_setlemt_amt)
        end)

      latest_report_date =
        deals
        |> Enum.max_by(& &1.report_date)

      %{
        account_name: account_name,
        clean_setlemt_amt: total_clean_setlemt_amt,
        report_date: latest_report_date.report_date
      }
    end)
  end


  def get_account_numbers_and_names do
    account_numbers = [
      "131000ZAR0105008", "131000ZAR0105009", "131000BWP0170701",
      "131000KESE0117091", "131000JPY0112081", "131000INR0112192",
      "131000SZL0117143", "131000MWK0117111", "131000TZS0117092",
      "131000USD0117152", "131000ZAR0107000", "131000CNY0112193",
      "131000MWK0112111", "131000INR0112197", "131000USD0112153",
      "131000GBP0112063", "131000CAD0112022", "131000SEK0112141",
      "131000SEK0112142", "131000NZD0112131", "131000EUR0112051",
      "131000AUD0112001", "131000GBP0112061", "131000USD0112154",
      "131000USD0112150", "131000EUR0112057", "131000ZAR0117035",
      "131000ZAR0117161", "131000USD0118159", "131000ZAR0118161",
      "131000CHF0117033", "131000NOK0117122", "131000AUD0117036",
      "131000DKK0117042", "131000CNH0117191", "131000NZD0117037",
      "131000CNY0112196", "131000CNY0113033", "131000EUR0112057"
    ]

    accounts =
      NostroAccounts
      |> where([n], n.account_number in ^account_numbers)
      |> Repo.all()

    accounts
    |> Enum.map(fn n ->
      normalized_name =
        if (String.contains?(n.account_name, "STANDARD BANK") and
              (String.contains?(n.account_name, "SOUTH AFRICA") or
               String.contains?(n.account_name, "SOUTHAFRICA") or
               String.contains?(n.account_name, "SB"))) or
             String.contains?(n.account_name, "BANK 04 FX Curr") do
          "Standard Bank South Africa"
        else
          n.account_name
        end

      %{
        account_number: n.account_number,
        account_name: normalized_name,
        report_date: n.report_date,
        lcy_book_bal7: n.lcy_book_bal7,
      }
    end)
    |> Enum.group_by(fn n -> {n.account_name,n.lcy_book_bal7, n.report_date} end, & &1.account_number)
    |> Enum.map(fn {{account_name,lcy_book_bal7, report_date}, account_numbers} ->
      %{
        account_name: account_name,
        lcy_book_bal7: lcy_book_bal7,
        report_date: report_date,
        account_numbers: account_numbers

      }
    end)
  end


  def get_all_exchange_placements do
    base_query()
    |> order_by([e], desc: e.report_date)
    |> Repo.all()
    |> normalize_and_deduplicate_entries()
  end

  defp base_query do
    from(e in ExchangePlacement,
      select: %{
        account_name: e.institution_name,
        maximum: e.maximum,
        rating: e.rating,
        rating_agency: e.rating_agency,
        report_date: e.report_date,
        status: e.status
      }
    )
  end

  defp normalize_and_deduplicate_entries(placements) do
    placements
    |> Enum.map(&normalize_account_name/1)
    |> Enum.group_by(& &1.account_name)
    |> Enum.map(fn {account_name, entries} ->
      case entries
           |> Enum.filter(&complete_entry?/1)
           |> Enum.take(1)
           |> List.first() do
        nil ->
          nil

        entry ->
          %{
            account_name: account_name,
            entry:
              Map.take(entry, [:maximum, :rating, :rating_agency, :report_date, :status])
          }
      end
    end)
    |> Enum.reject(&is_nil/1)
  end

  defp normalize_account_name(%{account_name: name} = placement) do
    normalized_name =
      if standard_bank_match?(name), do: "Standard Bank South Africa", else: name

    %{placement | account_name: normalized_name}
  end

  defp standard_bank_match?(name) do
    name = String.upcase(name)
    String.contains?(name, "STANDARD BANK") and
      (String.contains?(name, "SOUTH AFRICA") or String.contains?(name, "SOUTHAFRICA") or String.contains?(name, "SB"))
  end

  defp complete_entry?(%{maximum: max, rating: rat, rating_agency: ra, report_date: rd, status: stat}) do
    [max, rat, ra, rd, stat] |> Enum.all?(& &1)
  end



  alias MisReports.SourceData.FinnacleTb

  @doc """
  Returns the list of tbl_daily_finnacle_tb.

  ## Examples

      iex> list_tbl_daily_finnacle_tb()
      [%FinnacleTb{}, ...]

  """
  def list_tbl_daily_finnacle_tb do
    Repo.all(FinnacleTb)
  end

  @doc """
  Gets a single finnacle_tb.

  Raises `Ecto.NoResultsError` if the Finnacle tb does not exist.

  ## Examples

      iex> get_finnacle_tb!(123)
      %FinnacleTb{}

      iex> get_finnacle_tb!(456)
      ** (Ecto.NoResultsError)

  """
  def get_finnacle_tb!(id), do: Repo.get!(FinnacleTb, id)

  @doc """
  Creates a finnacle_tb.

  ## Examples

      iex> create_finnacle_tb(%{field: value})
      {:ok, %FinnacleTb{}}

      iex> create_finnacle_tb(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_finnacle_tb(attrs \\ %{}) do
    %FinnacleTb{}
    |> FinnacleTb.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a finnacle_tb.

  ## Examples

      iex> update_finnacle_tb(finnacle_tb, %{field: new_value})
      {:ok, %FinnacleTb{}}

      iex> update_finnacle_tb(finnacle_tb, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_finnacle_tb(%FinnacleTb{} = finnacle_tb, attrs) do
    finnacle_tb
    |> FinnacleTb.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a finnacle_tb.

  ## Examples

      iex> delete_finnacle_tb(finnacle_tb)
      {:ok, %FinnacleTb{}}

      iex> delete_finnacle_tb(finnacle_tb)
      {:error, %Ecto.Changeset{}}

  """
  def delete_finnacle_tb(%FinnacleTb{} = finnacle_tb) do
    Repo.delete(finnacle_tb)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking finnacle_tb changes.

  ## Examples

      iex> change_finnacle_tb(finnacle_tb)
      %Ecto.Changeset{data: %FinnacleTb{}}

  """
  def change_finnacle_tb(%FinnacleTb{} = finnacle_tb, attrs \\ %{}) do
    FinnacleTb.changeset(finnacle_tb, attrs)
  end

  alias MisReports.SourceData.QuarterlySrcFiles

  @doc """
  Returns the list of tbl_quarterly_src_files.

  ## Examples

      iex> list_tbl_quarterly_src_files()
      [%QuarterlySrcFiles{}, ...]

  """
  def list_tbl_quarterly_src_files do
    Repo.all(QuarterlySrcFiles)
  end

  @doc """
  Gets a single quarterly_src_files.

  Raises `Ecto.NoResultsError` if the Quarterly src files does not exist.

  ## Examples

      iex> get_quarterly_src_files!(123)
      %QuarterlySrcFiles{}

      iex> get_quarterly_src_files!(456)
      ** (Ecto.NoResultsError)

  """
  def get_quarterly_src_files!(id), do: Repo.get!(QuarterlySrcFiles, id)

  def get_quarterly_report_src_file_pending_upload() do
    QuarterlySrcFiles
    |> where(status: "PENDING_UPLOAD")
    |> Repo.all()
  end

  @doc """
  Creates a quarterly_src_files.

  ## Examples

      iex> create_quarterly_src_files(%{field: value})
      {:ok, %QuarterlySrcFiles{}}

      iex> create_quarterly_src_files(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_quarterly_src_files(attrs \\ %{}) do
    %QuarterlySrcFiles{}
    |> QuarterlySrcFiles.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a quarterly_src_files.

  ## Examples

      iex> update_quarterly_src_files(quarterly_src_files, %{field: new_value})
      {:ok, %QuarterlySrcFiles{}}

      iex> update_quarterly_src_files(quarterly_src_files, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_quarterly_src_files(%QuarterlySrcFiles{} = quarterly_src_files, attrs) do
    quarterly_src_files
    |> QuarterlySrcFiles.changeset(attrs)
    |> Repo.update()
  end



  @doc """
  Deletes a quarterly_src_files.

  ## Examples

      iex> delete_quarterly_src_files(quarterly_src_files)
      {:ok, %QuarterlySrcFiles{}}

      iex> delete_quarterly_src_files(quarterly_src_files)
      {:error, %Ecto.Changeset{}}

  """
  def delete_quarterly_src_files(%QuarterlySrcFiles{} = quarterly_src_files) do
    Repo.delete(quarterly_src_files)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking quarterly_src_files changes.

  ## Examples

      iex> change_quarterly_src_files(quarterly_src_files)
      %Ecto.Changeset{data: %QuarterlySrcFiles{}}

  """
  def change_quarterly_src_files(%QuarterlySrcFiles{} = quarterly_src_files, attrs \\ %{}) do
    QuarterlySrcFiles.changeset(quarterly_src_files, attrs)
  end

  alias MisReports.SourceData.CmmpProduct

  @doc """
  Returns the list of tbl_cmmp_product.

  ## Examples

      iex> list_tbl_cmmp_product()
      [%CmmpProduct{}, ...]

  """
  def list_tbl_cmmp_product do
    Repo.all(CmmpProduct)
  end

  @doc """
  Gets a single cmmp_product.

  Raises `Ecto.NoResultsError` if the Cmmp product does not exist.

  ## Examples

      iex> get_cmmp_product!(123)
      %CmmpProduct{}

      iex> get_cmmp_product!(456)
      ** (Ecto.NoResultsError)

  """
  def get_cmmp_product!(id), do: Repo.get!(CmmpProduct, id)


  @doc """
  Creates a cmmp_product.

  ## Examples

      iex> create_cmmp_product(%{field: value})
      {:ok, %CmmpProduct{}}

      iex> create_cmmp_product(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_cmmp_product(attrs \\ %{}) do
    %CmmpProduct{}
    |> CmmpProduct.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a cmmp_product.

  ## Examples

      iex> update_cmmp_product(cmmp_product, %{field: new_value})
      {:ok, %CmmpProduct{}}

      iex> update_cmmp_product(cmmp_product, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_cmmp_product(%CmmpProduct{} = cmmp_product, attrs) do
    cmmp_product
    |> CmmpProduct.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a cmmp_product.

  ## Examples

      iex> delete_cmmp_product(cmmp_product)
      {:ok, %CmmpProduct{}}

      iex> delete_cmmp_product(cmmp_product)
      {:error, %Ecto.Changeset{}}

  """
  def delete_cmmp_product(%CmmpProduct{} = cmmp_product) do
    Repo.delete(cmmp_product)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking cmmp_product changes.

  ## Examples

      iex> change_cmmp_product(cmmp_product)
      %Ecto.Changeset{data: %CmmpProduct{}}

  """
  def change_cmmp_product(%CmmpProduct{} = cmmp_product, attrs \\ %{}) do
    CmmpProduct.changeset(cmmp_product, attrs)
  end

  alias MisReports.SourceData.CmmpBranches

  @doc """
  Returns the list of tbl_cmmp_branches.

  ## Examples

      iex> list_tbl_cmmp_branches()
      [%CmmpBranches{}, ...]

  """
  def list_tbl_cmmp_branches do
    Repo.all(CmmpBranches)
  end

  @doc """
  Gets a single cmmp_branches.

  Raises `Ecto.NoResultsError` if the Cmmp branches does not exist.

  ## Examples

      iex> get_cmmp_branches!(123)
      %CmmpBranches{}

      iex> get_cmmp_branches!(456)
      ** (Ecto.NoResultsError)

  """
  def get_cmmp_branches!(id), do: Repo.get!(CmmpBranches, id)

  @doc """
  Creates a cmmp_branches.

  ## Examples

      iex> create_cmmp_branches(%{field: value})
      {:ok, %CmmpBranches{}}

      iex> create_cmmp_branches(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_cmmp_branches(attrs \\ %{}) do
    %CmmpBranches{}
    |> CmmpBranches.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a cmmp_branches.

  ## Examples

      iex> update_cmmp_branches(cmmp_branches, %{field: new_value})
      {:ok, %CmmpBranches{}}

      iex> update_cmmp_branches(cmmp_branches, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_cmmp_branches(%CmmpBranches{} = cmmp_branches, attrs) do
    cmmp_branches
    |> CmmpBranches.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a cmmp_branches.

  ## Examples

      iex> delete_cmmp_branches(cmmp_branches)
      {:ok, %CmmpBranches{}}

      iex> delete_cmmp_branches(cmmp_branches)
      {:error, %Ecto.Changeset{}}

  """
  def delete_cmmp_branches(%CmmpBranches{} = cmmp_branches) do
    Repo.delete(cmmp_branches)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking cmmp_branches changes.

  ## Examples

      iex> change_cmmp_branches(cmmp_branches)
      %Ecto.Changeset{data: %CmmpBranches{}}

  """
  def change_cmmp_branches(%CmmpBranches{} = cmmp_branches, attrs \\ %{}) do
    CmmpBranches.changeset(cmmp_branches, attrs)
  end

  def get_debtors_book_analysis!(id), do: Repo.get!(DebtorsBookAnalysis, id)

  def change_debtors_book_analysis(%DebtorsBookAnalysis{} = debtors_book_analysis, attrs \\ %{}) do
    DebtorsBookAnalysis.changeset(debtors_book_analysis, attrs)
  end

  def list_tbl_debtors_book_analysis(params) do
    DebtorsBookAnalysis
    |> preload([:checker, :maker])
    |> debtors_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp debtors_isearch_filter(query, nil), do: query

  defp debtors_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  end

  def get_debtors_book_analysis_by_date(date, type) do
    DebtorsBookAnalysis
    |> where([a], a.report_date == ^date and a.type == ^type)
    |> select([a], %{
      "type" => a.type,
      "allowance_for_losses_mortgages" => a.allowance_for_losses_mortgages,
      "allowance_for_losses_leases" => a.allowance_for_losses_leases,
      "allowance_for_losses_unsecured" => a.allowance_for_losses_unsecured,
      "allowance_for_losses_revolving" => a.allowance_for_losses_revolving,

      "number_of_accounts_mortgages" => a.number_of_accounts_mortgages,
      "number_of_accounts_leases" => a.number_of_accounts_leases,
      "number_of_accounts_unsecured" => a.number_of_accounts_unsecured,
      "number_of_accounts_revolving" => a.number_of_accounts_revolving,
    })
    |> Repo.all()
  end

  def get_all_debtors_book_analysis_by_date(date) do
    DebtorsBookAnalysis
    |> where([a], a.report_date == ^date)
    |> select([a], %{
      "type" => a.type,
      "allowance_for_losses_mortgages" => a.allowance_for_losses_mortgages,
      "allowance_for_losses_leases" => a.allowance_for_losses_leases,
      "allowance_for_losses_unsecured" => a.allowance_for_losses_unsecured,
      "allowance_for_losses_revolving" => a.allowance_for_losses_revolving,

      "number_of_accounts_mortgages" => a.number_of_accounts_mortgages,
      "number_of_accounts_leases" => a.number_of_accounts_leases,
      "number_of_accounts_unsecured" => a.number_of_accounts_unsecured,
      "number_of_accounts_revolving" => a.number_of_accounts_revolving,
    })
    |> Repo.all()
  end
end
