defmodule MisReports.SourceData.QuarterlySrcFiles do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_quarterly_src_files" do
    field :date, :date
    field :filename, :string
    field :month, :string
    field :status, :string, default: "PENDING_UPLOAD"
    field :year, :string
    field :reference, :string, default: Ecto.UUID.generate()
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(quarterly_src_files, attrs) do
    quarterly_src_files
    |> cast(attrs, [:filename, :date, :month, :year, :status, :maker_id, :checker_id, :reference])
    |> validate_required([:filename, :date, :month, :year, :status])
  end
end
