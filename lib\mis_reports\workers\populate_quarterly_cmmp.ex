defmodule MisReports.Workers.PopulateQuarterlyCmmp do
  alias MisReports.Workers.Utils
  alias MisReports.Utilities


  def perform(data) do
    IO.inspect("------------------------POPULATE EXPORTING-----------------------------")
    rate =
      case Utilities.get_exchange_rate_by_date_and_code("#{data.end_date}", "USD") do
        nil -> 1.0
        rate -> Decimal.to_float(rate[:exchange_rate_lcy])
      end

    file = gen_file(data)

    case Excelizer.open(file, &populate(&1, data, rate)) do
      :ok -> {:ok,  file}
      error -> error
    end
  end

  def gen_file(item) do
    dir = MisReports.Utilities.get_directory_params()
    datetime = Timex.local() |> Timex.format!("%Y%m%d%H%M", :strftime)
    name = "Cmmp_Report_#{datetime}.xlsx"
    MisReports.Prudentials.update_cmmp_report(item, %{filename: name})
    dest_file = "#{dir.complete}/#{name}"
    template_file = "#{dir.templates}/Cmmp_template.xlsx"
    File.copy!(template_file, dest_file)
    dest_file
  end

  def populate(file_id, data, _rate) do
    agriculture_large(file_id, data)
    business_small(file_id, data)
    business_large(file_id, data)
    households_individuals(file_id, data)
    government(file_id, data)
    geographical(file_id, data)
    gen_info(file_id, data)
    totals(file_id, data)
  end

  def gen_info(file_id, data) do
    settings = MisReports.Utilities.get_comapany_settings_params()
    start_date = data.start_date |> Timex.format!("{0D}-{0M}-{YYYY}")
    end_date = data.end_date |>Timex.format!("{0D}-{0M}-{YYYY}")
    inst_code = settings.institution_code
    year = data.year
    month = data.month


    # Insert date and institution code into different sheets
    #Agriculuture Small
    Excelizer.Cell.set_cell_value(file_id, "Agriculture - Small", "B2", "string", "#{inst_code}")
    Excelizer.Cell.set_cell_value(file_id, "Agriculture - Small", "E4", "string", year)
    Excelizer.Cell.set_cell_value(file_id, "Agriculture - Small", "E5", "string", month)


    Excelizer.Workbook.save(file_id)
  end

  def agriculture_large(file_id, data) do
    data.agriculture_large
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["I35", "G41", "E39", "I89", "F114", "G20", "J80", "G133", "H137", "F115",
       "L125", "J90", "G23", "G37", "K34", "I39", "F117", "G36", "J123", "I137",
       "J83", "J133", "B33", "H37", "H118", "C82", "G42", "C34", "K136", "I33", "I36",
       "D133", "H115", "E78", "I32", "F38", "B37", "F116", "H23", "G39", "I100",
       "I94", "G71", "C101", "G124", "D116", "I13", "C105", "G135", "I138", "J39",
       "B41", "H36", "J40", "C78", "E101", "I81", "H38", "C80", "G14", "D124", "L134",
       "G137", "I90", "B39", "I105", "F32", "G134", "G128", "K40", "G123", "J134",
       "C91", "C39", "H127", "J82", "E103", "I37", "J34", "I104", "J36", "C36", "H39",
       "F36", "G60", "D128", "J41", "J124", "L135", "K33", "L133", "J126", "I133",
       "L33", "F39", "F33", "J92", "F134", "J35", "G136", "H128", "E90", "C103",
       "I125", "H71", "L41", "E93", "D118", "E34", "K126", "I34", "G127", "G32",
       "J79", "J127", "E42", "L38", "G12", "I42", "J81", "K41", "G118", "I136", "C32",
       "L127", "L40", "J100", "H42", "I101", "D117", "F127", "J137", "F138", "C94",
       "F37", "L138", "I20", "I82", "F124", "I123", "J103", "K37", "K127", "H33",
       "D136", "B34", "J42", "H116", "E82", "C104", "L37", "H124", "H134", "C40",
       "G138", "C79", "E102", "G33", "L32", "D123", "C93", "D135", "C35", "C102",
       "D137", "E36", "D114", "H125", "H60", "B38", "G13", "I134", "L124", "I79",
       "J89", "I135", "F34", "H41", "E41", "J135", "H61", "J93", "L36", "G115",
       "I103", "G116", "I10", "G11", "K128", "H135", "L126", "K35", "L128", "I12",
       "J105", "E91", "I126", "I128", "I40", "G34", "K124", "C92", "E83", "C90",
       "K32", "K135", "C89", "F136", "K36", "K133", "E89", "K39", "D138", "C42",
       "J138", "B36", "H123", "J32", "L39", "I92", "D134", "I78", "F41", "J102",
       "K38", "I41", "I124", "C100", "G114", "C81", "E80", "L42", "B35", "H35",
       "D126", "I83", "H20", "J128", "C41", "B42", "B40", "D115", "J136", "E81",
       "G126", "H53", "I127", "H32", "J33", "F137", "K137", "I80", "H126", "H114",
       "B32", "I91", "D127", "E32", "I93", "L137", "F123", "K125", "F40", "F128",
       "H138", "J37", "L34", "G125", "C38", "H133", "I11", "F135", "E105", "F35",
       "J101", "E38", "F125", "C37", "J78", "F42", "E33", "D125", "G38", "H117",
       "I102", "F118", "G40", "G53", "J38", "K42", "F126", "J104", "E79", "G61",
       "E35", "K123", "E94", "G117", "H34", "H136", "E104", "L123", "I38", "H40",
       "G10", "E40", "J91", "F133", "K138", "L35", "G35", "J94", "C33", "E100",
       "L136", "J125", "C83", "E92", "E37", "K134"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
       if(index in [
          "E32", "E33", "E34", "E35", "E36", "E37", "E38", "E39", "E40", "E41", "E42",
          "F32", "F33", "F34", "F35", "F36", "F37", "F38", "F39", "F40", "F41", "F42",
          "I32", "I33", "I34", "I35", "I36", "I37", "I38", "I39", "I40", "I41", "I42",
          "J32", "J33", "J34", "J35", "J36", "J37", "J38", "J39", "J40", "J41", "J42",
          "D128", "F128", "G128", "H128", "I128", "J128", "K128", "L128",
          "D138", "F138", "G138", "H138", "I138", "J138", "K138", "L138"
          ],
          do:
            Excelizer.Cell.set_cell_value(file_id, "Agriculture Large", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.div(Decimal.new("100")) |> Decimal.to_float()),
          else:
            Excelizer.Cell.set_cell_value(file_id, "Agriculture Large", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()),
          )

      {index, value} when value in  [0 ,"0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Agriculture Large", index, "int", 0)

      {index, value} when  is_integer(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Agriculture Large", index, "int", value)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Agriculture Large", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Agriculture Large", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def business_large(file_id, data) do
    data.business_large
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["I35", "G41", "E39", "I89", "F114", "G20", "J80", "G133", "H137", "F115",
       "L125", "J90", "G37", "K34", "I39", "F117", "G36", "H52", "J123", "I137",
       "F113", "J133", "B33", "H37", "C82", "C34", "K136", "I33", "I36", "D133",
       "H115", "L132", "E78", "I32", "F122", "F38", "H66", "B37", "F116", "H113",
       "G39", "K31", "I100", "I122", "C101", "G124", "B31", "D113", "E77", "D116",
       "I13", "G135", "J39", "B41", "H36", "J40", "C78", "E101", "I81", "H38", "C80",
       "G14", "D124", "L134", "G137", "I90", "B39", "F32", "G134", "K40", "G123",
       "D132", "J134", "C91", "G22", "C39", "H132", "H127", "J82", "I99", "E103",
       "I37", "J34", "I104", "J36", "C36", "H39", "F36", "J41", "J124", "L135",
       "G122", "K33", "L133", "G66", "E31", "J126", "I133", "L33", "F39", "F33",
       "J92", "F134", "J35", "G136", "H22", "E90", "C103", "L31", "I125", "L41",
       "E93", "E34", "I132", "K126", "I34", "G127", "J31", "G32", "J79", "J127",
       "L38", "G12", "C99", "J81", "K41", "I136", "C32", "L127", "L40", "J100",
       "I101", "D117", "F127", "J137", "F37", "I20", "G113", "I82", "C88", "F31",
       "J88", "H122", "F124", "I123", "J103", "E88", "K37", "F132", "K127", "H33",
       "H55", "D136", "B34", "I77", "H116", "E82", "C104", "L37", "H124", "H134",
       "C40", "C79", "E102", "G33", "L32", "D123", "C93", "D135", "C35", "C102",
       "D137", "E36", "D114", "H125", "B38", "C31", "G13", "I134", "L124", "I79",
       "J89", "I135", "F34", "H41", "G52", "G31", "E41", "J135", "J93", "L36", "G115",
       "I103", "G116", "I10", "G11", "H135", "L126", "K35", "J99", "I12", "E91",
       "I126", "I40", "G34", "D122", "K124", "C92", "C90", "K32", "E99", "K135",
       "C89", "F136", "K36", "L122", "K133", "E89", "K39", "G132", "B36", "H123",
       "J32", "L39", "I92", "H56", "D134", "I78", "F41", "J102", "K38", "I41", "I124",
       "C100", "G114", "C81", "E80", "B35", "H31", "H35", "D126", "H20", "J122",
       "C41", "G56", "B40", "D115", "J136", "J132", "E81", "G126", "I127", "H32",
       "J33", "F137", "K137", "I80", "H126", "H114", "B32", "I91", "D127", "E32",
       "I93", "L137", "F123", "K125", "F40", "J37", "L34", "G125", "C38", "H133",
       "I11", "F135", "F35", "G55", "J101", "E38", "F125", "C37", "J78", "K122",
       "E33", "D125", "G38", "I31", "H117", "I102", "G40", "J38", "F126", "J104",
       "E79", "E35", "K123", "C77", "G117", "H34", "H136", "K132", "E104", "L123",
       "I38", "H40", "G10", "E40", "I88", "J91", "F133", "L35", "G35", "C33", "E100",
       "L136", "J125", "J77", "E92", "E37", "K134"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        if(index in [
          "E31", "E32", "E33", "E34", "E35", "E36", "E37", "E38", "E39", "E40", "E41",
          "F31", "F32", "F33", "F34", "F35", "F36", "F37", "F38", "F39", "F40", "F41",
          "I31", "I32", "I33", "I34", "I35", "I36", "I37", "I38", "I39", "I40", "I41",
          "J31", "J32", "J33", "J34", "J35", "J36", "J37", "J38", "J39", "J40", "J41",
          "D127", "F127", "G127", "H127", "I127", "J127", "K127", "L127",
          "D137", "F137", "G137", "H137", "I137", "J137", "K137", "L137"
          ],
          do:
            Excelizer.Cell.set_cell_value(file_id, "Business Large", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.div(Decimal.new("100")) |> Decimal.to_float()),
          else:
            Excelizer.Cell.set_cell_value(file_id, "Business Large", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()),
          )

      {index, value} when value in  [0, "0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Business Large", index, "int", 0)

      {index, value} when  is_integer(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Business Large", index, "int", value)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Business Large", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id, "Business Large", index, "string", value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def business_small(file_id, data) do
    data.business_small
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["K130", "I35", "G41", "J86", "E39", "J85", "H137", "G37", "I39", "F117", "G36",
       "I140", "I137", "F44", "H63", "J83", "H37", "H118", "C82", "G42", "C95",
       "K136", "G141", "J84", "I36", "H59", "H26", "J140", "F139", "H119", "F38",
       "H66", "B37", "C106", "I141", "E96", "G39", "I94", "E44", "K43", "J131", "C86",
       "I13", "C105", "I138", "J39", "B41", "H36", "H139", "J40", "G121", "I81",
       "I131", "H38", "I96", "G14", "G137", "G139", "D130", "L45", "B39", "I105",
       "G128", "K40", "I44", "F121", "J95", "H45", "C39", "H127", "I130", "J82",
       "E103", "D129", "D140", "G44", "I37", "E107", "I104", "K139", "J36", "C36",
       "H39", "F36", "G60", "D128", "I107", "J41", "C43", "D141", "I84", "G66",
       "J126", "F39", "J92", "I106", "D139", "J35", "G136", "H128", "C103", "G120",
       "J45", "L141", "L41", "E93", "H70", "D118", "I45", "K126", "G127", "H25",
       "J127", "H141", "E42", "L38", "G12", "F130", "L139", "I42", "L43", "B44",
       "J81", "K41", "G118", "I136", "E84", "L127", "L40", "H42", "D117", "F127",
       "J137", "G59", "F120", "F138", "C94", "C85", "F37", "L138", "I43", "I82",
       "G129", "E45", "C96", "C107", "C84", "G131", "J103", "L140", "K37", "J141",
       "K127", "E106", "L131", "I97", "D136", "J43", "D119", "B45", "H140", "G70",
       "J42", "G43", "I95", "F140", "E82", "C104", "J139", "C45", "L37", "F141",
       "C40", "G45", "G138", "I86", "C93", "C35", "H129", "D137", "E36", "J96", "H60",
       "B38", "G13", "D120", "H41", "I108", "E41", "J93", "L36", "I103", "C108",
       "I10", "G11", "F43", "K128", "L126", "K35", "L128", "J44", "I12", "J105",
       "J106", "I126", "I128", "I40", "G63", "F45", "C92", "H120", "H130", "E83",
       "J97", "F136", "K36", "J129", "H43", "G130", "K39", "G119", "D138", "C44",
       "C42", "J138", "B36", "D121", "L39", "I92", "H56", "K45", "F41", "K38", "I41",
       "E86", "C81", "L42", "B35", "H35", "D126", "I83", "J128", "C41", "E85", "G56",
       "B42", "B40", "H131", "J136", "E81", "G126", "I127", "F137", "K137", "H126",
       "L44", "E43", "H44", "D127", "I93", "B43", "L137", "F40", "F128", "H138",
       "J37", "F131", "E97", "K140", "J107", "C38", "I139", "I11", "L129", "E105",
       "F35", "E38", "E108", "C37", "F42", "G38", "H117", "J130", "F118", "G40",
       "F119", "K141", "E95", "J38", "J108", "K42", "F126", "J104", "G25", "H121",
       "G140", "D131", "E35", "G26", "I85", "I129", "E94", "G117", "F129", "H136",
       "E104", "K131", "L130", "I38", "H40", "G10", "E40", "K138", "L35", "G35",
       "J94", "L136", "C83", "K129", "E92", "C97", "K44", "E37"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        if(index in [
          "E35", "E36", "E37", "E38", "E39", "E40", "E41", "E42", "E43", "E44", "E45",
          "F35", "F36", "F37", "F38", "F39", "F40", "F41", "F42", "F43", "F44", "F45",
          "I35", "I36", "I37", "I38", "I39", "I40", "I41", "I42", "I43", "I44", "I45",
          "J35", "J36", "J37", "J38", "J39", "J40", "J41", "J42", "J43", "J44", "J45",
          "D131", "F131", "G131", "H131", "I131", "J131", "K131", "L131",
          "D141", "F141", "G141", "H141", "I141", "J141", "K141", "L141"
          ],
          do:
            Excelizer.Cell.set_cell_value(file_id, "Business Small", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.div(Decimal.new("100")) |> Decimal.to_float()),
          else:
            Excelizer.Cell.set_cell_value(file_id, "Business Small", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()),
          )

      {index, value} when value in  [0, "0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Business Small", index, "int", 0)

      {index, value} when  is_integer(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Business Small", index, "int", value)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Business Small", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id, "Business Small", index, "string", value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def households_individuals(file_id, data) do
    data.households_individuals
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["K144", "I35", "G41", "E39", "J120", "G133", "G79", "G23", "G37", "H54", "K34",
       "G27", "H27", "I27", "G28", "H28", "I28",
       "I39", "G36", "H52", "J150", "F152", "I140", "B33", "H37", "K151", "C110",
       "L154", "G42", "C34", "C95", "J142", "G141", "I33", "I36", "H59", "F144",
       "D133", "J140", "F139", "L142", "F38", "B37", "E121", "C106", "I141", "H23",
       "G142", "E96", "G150", "G39", "I116", "J121", "G77", "C118", "I144", "H142",
       "G149", "I94", "K43", "C98", "J98", "E109", "I13", "C105", "J39", "B41",
       "I143", "H36", "H139", "I151", "J40", "F142", "H64", "H38", "I96", "G14",
       "G139", "D130", "L153", "K149", "B39", "I105", "G134", "K40", "D132", "H153",
       "I117", "G143", "J95", "J143", "C39", "H132", "I99", "G67", "D140", "I37",
       "E107", "J34", "K139", "J36", "C36", "H143", "K153", "H39", "I153", "F36",
       "G60", "I107", "I110", "J41", "C43", "D141", "K33", "E110", "G151", "H152",
       "L33", "F39", "D144", "F33", "G144", "I106", "F134", "D139", "J35", "I149",
       "D150", "L141", "L41", "E34", "I109", "I14", "I34", "D151", "J153", "H149",
       "K154", "H141", "H24", "L152", "E42", "L38", "G12", "F130", "C99", "L139",
       "I42", "L43", "K41", "J152", "D142", "F143", "L40", "D149", "H42", "L150",
       "G59", "G152", "F151", "C94", "F37", "I43", "D152", "E119", "C96", "C107",
       "F149", "F154", "G131", "K152", "L140", "K37", "F132", "J141", "I142", "H33",
       "E106", "I97", "J43", "B34", "H140", "J42", "G43", "I95", "I154", "F140",
       "H87", "J139", "L37", "F141", "H134", "C40", "K142", "G33", "D153", "C35",
       "G85", "E36", "I119", "J96", "J109", "I118", "G64", "H60", "B38", "C121",
       "E118", "G13", "F34", "H41", "L149", "G52", "J117", "I108", "E41", "G54",
       "H79", "E120", "J110", "L36", "C108", "G11", "F43", "H67", "K35", "I23", "J99",
       "J116", "H78", "I12", "J105", "J106", "I150", "I40", "G34", "H130", "D154",
       "E99", "J97", "F153", "K36", "H43", "G130", "C116", "E116", "K39", "J154",
       "C42", "G24", "G132", "B36", "C119", "L39", "D134", "F41", "K38", "H154",
       "I41", "J118", "G15", "F150", "L42", "B35", "H35", "J144", "I120", "J149",
       "C41", "G87", "I152", "B42", "B40", "H131", "J33", "C117", "E43", "H144",
       "G49", "E98", "B43", "F40", "J37", "F131", "L34", "E97", "K140", "J107", "C38",
       "I139", "K150", "H133", "H151", "I11", "L151", "E105", "H49", "F35", "E38",
       "C120", "E108", "C37", "F42", "E33", "G38", "H85", "G40", "K141", "E95",
       "G154", "J38", "J108", "K42", "H77", "J119", "L144", "G140", "D131", "E35",
       "E94", "H34", "C109", "L143", "G78", "I98", "I38", "H40", "E40", "E117",
       "F133", "K143", "L35", "G35", "D143", "J94", "C33", "H150", "J151", "I121",
       "C97", "E37", "G153"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        if(index in [
          "D144", "F144", "G144", "H144", "I144", "J144", "K144", "L144",
          "D154", "F154", "G154", "H154", "I154", "J154", "K154", "L154",
          ],
          do:
            Excelizer.Cell.set_cell_value(file_id, "HseHolds & Indiv", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.div(Decimal.new("100")) |> Decimal.to_float()),
          else:
            Excelizer.Cell.set_cell_value(file_id, "HseHolds & Indiv", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()),
          )

      {index, value} when value in  [0, "0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "HseHolds & Indiv", index, "int", 0)

      {index, value} when  is_integer(value) ->
          Excelizer.Cell.set_cell_value(file_id, "HseHolds & Indiv", index, "int", value)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "HseHolds & Indiv", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"HseHolds & Indiv", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def government(file_id, data) do
    data.government
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["D79", "H19", "E29", "I35", "G62", "L82", "E28", "J80", "G79", "G23", "J70",
       "G37", "K34", "E30", "G36", "F80", "I30", "C28", "H63", "J83", "K71", "B33",
       "H37", "L80", "D80", "H46", "C34", "J84", "I33", "I36", "H82", "I32", "F38",
       "B37", "D62", "H23", "I73", "K31", "I28", "H83", "L30", "G71", "B31", "D82",
       "L83", "I13", "K82", "F81", "F64", "H36", "G47", "D71", "H64", "I81", "H38",
       "G14", "D73", "F71", "F32", "B28", "G28", "H29", "H45", "H69", "F79", "D74",
       "J28", "J82", "L84", "G83", "G44", "I37", "L73", "J34", "H74", "J36", "C36",
       "F36", "L79", "G60", "K33", "I84", "D60", "F74", "L72", "E31", "L33", "H80",
       "J69", "D64", "F33", "K80", "J35", "L70", "L31", "K72", "K79", "H71", "H70",
       "E34", "G82", "I34", "J31", "G32", "J79", "L38", "G12", "J81", "H30", "C32",
       "K30", "I71", "G72", "F30", "F37", "D63", "I82", "F31", "F63", "F60", "K37",
       "H33", "B34", "G69", "H73", "K70", "G70", "G30", "K69", "G43", "L37", "F72",
       "G45", "D83", "G33", "L32", "C35", "I72", "L28", "E36", "G80", "F29", "K83",
       "G46", "G64", "H60", "B38", "C31", "G13", "D72", "I79", "F34", "L29", "G31",
       "G84", "H61", "F28", "H79", "L36", "I29", "I10", "G11", "K35", "J71", "I12",
       "F73", "G34", "I70", "G63", "K32", "F82", "I69", "I74", "G19", "K36", "H43",
       "K73", "B29", "B36", "F61", "J32", "K74", "L71", "H81", "J72", "K38", "C30",
       "F70", "F69", "G73", "K28", "B35", "L69", "H31", "H35", "I83", "H28", "K81",
       "F84", "L74", "H32", "J33", "F62", "I80", "H84", "H44", "B32", "E32", "J29",
       "J37", "L34", "L81", "C38", "I11", "J74", "F35", "E38", "C37", "J73", "K84",
       "E33", "G38", "I31", "C29", "G29", "G74", "J38", "D61", "D81", "D69", "H72",
       "G61", "G81", "E35", "H47", "H34", "B30", "H62", "I38", "G10", "K29", "L35",
       "G35", "D70", "C33", "F83", "D84", "J30", "E37"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        if(index in [
          "E28", "E29", "E30", "E31", "E32", "E33", "E34", "E35", "E37", "E38",
          "F28", "F29", "F30", "F31", "F32", "F33", "F34", "F35", "F37", "F38",
          "I28", "I29", "I30", "I31", "I32", "I33", "I34", "I35", "I37", "I38",
          "J28", "J29", "J30", "J31", "J32", "J33", "J34", "J35", "J37", "J38",
          "D74", "F74", "G74", "H74", "I74", "J74", "K74", "L74",
          "D84", "F84", "G84", "H84", "I84", "J84", "K84", "L84"
          ],
          do:
            Excelizer.Cell.set_cell_value(file_id, "Government", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.div(Decimal.new("100")) |> Decimal.to_float()),
          else:
            Excelizer.Cell.set_cell_value(file_id, "Government", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()),
          )

      {index, value} when value in  [0, "0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Government", index, "int", 0)

      {index, value} when  is_integer(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Government", index, "int", value)


      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Government", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Government", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def geographical(file_id, data) do
    data.geographical
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["B124", "I50", "B51", "D79", "E29", "E115", "I35", "G283", "F247", "I312",
       "D234", "E50", "G41", "I47", "F145", "F111", "B197", "G252", "I24", "D207",
       "B23", "G62", "B128", "E263", "B300", "E39", "E28", "D210", "I162", "F303",
       "I89", "F114", "G20", "G51", "F232", "F48", "G174", "D75", "B204", "F16",
       "F115", "F291", "B312", "G79", "D173", "E248", "F216", "G23", "G37", "E219",
       "I39", "I300", "G261", "B55", "E217", "D92", "E30", "G36", "F299", "F80",
       "G50", "F152", "B90", "B172", "B138", "I140", "I75", "D158", "E294", "I30",
       "I137", "B281", "F113", "E225", "B160", "G188", "I263", "B295", "F186", "B254",
       "B33", "I46", "E299", "I15", "G280", "D80", "I219", "E252", "I278", "E14",
       "B291", "G286", "E292", "F109", "B219", "E208", "D186", "B311", "B70", "I186",
       "G141", "D206", "I264", "G248", "G107", "D252", "B309", "I33", "I36", "G177",
       "F295", "G180", "I268", "B185", "I298", "F139", "E78", "F122", "F38", "B264",
       "E24", "D299", "G233", "B37", "E121", "G236", "D175", "D62", "D297", "I252",
       "D298", "I141", "E65", "D216", "B136", "G142", "F203", "D110", "B153", "G39",
       "D286", "B78", "I294", "G77", "F314", "B48", "F252", "G93", "I28", "F235",
       "D310", "I100", "D250", "B179", "I122", "E16", "I49", "E15", "D23", "G71",
       "E227", "F55", "G124", "B31", "B140", "G298", "D113", "G242", "B47", "E77",
       "E51", "D319", "G276", "F208", "I257", "D53", "E109", "I172", "F318", "F196",
       "D82", "B113", "D239", "G135", "I138", "F172", "F64", "F76", "D172", "D202",
       "B41", "I143", "G202", "F304", "D285", "E66", "D20", "I196", "G90", "E75",
       "G47", "I256", "B66", "F268", "D291", "D71", "I277", "G121", "F212", "F142",
       "D54", "D212", "D242", "E48", "B50", "G266", "F286", "B186", "F254", "G14",
       "B302", "E317", "B203", "D124", "G176", "E300", "G137", "G139", "B191", "D130",
       "I90", "B286", "F293", "E191", "E271", "E319", "D267", "B265", "D295", "B268",
       "F71", "B212", "F174", "I228", "D105", "E177", "E285", "B39", "I105", "B210",
       "F283", "G285", "G134", "E113", "B71", "F237", "G128", "F276", "B28", "F309",
       "G28", "F315", "D220", "E220", "D293", "G123", "F292", "E198", "F91", "I211",
       "B106", "E211", "B162", "F121", "D257", "G143", "G173", "I48", "G179", "D271",
       "E196", "I216", "B93", "E134", "E221", "F218", "F228", "G22", "D317", "I198",
       "G246", "I180", "F79", "I130", "F278", "D46", "F22", "E256", "B174", "I99",
       "G287", "E314", "I297", "D129", "B261", "D140", "G296", "E254", "B298", "G263",
       "B283", "D265", "I37", "E235", "E107", "I242", "I104", "F160", "F317", "I220",
       "E145", "D294", "E155", "B62", "D55", "D34", "B148", "B192", "D91", "F205",
       "I153", "F36", "F264", "D302", "I113", "G60", "D128", "B59", "I208", "B252",
       "I54", "F220", "I60", "I107", "G292", "I110", "F177", "G122", "F207", "D141",
       "F185", "I202", "G195", "F49", "I84", "D60", "E232", "F108", "E110", "G66",
       "F287", "F192", "F297", "E31", "E296", "F211", "B314", "E276", "E195", "D156",
       "G314", "B236", "D313", "G311", "E216", "E60", "E141", "G191", "F39", "E68",
       "E280", "E156", "D64", "D287", "D90", "F33", "B270", "D39", "E239", "I295",
       "G97", "F257", "I106", "B220", "G247", "F134", "D139", "B232", "G136", "B292",
       "G193", "B317", "B246", "I17", "E250", "B310", "I157", "B187", "E90", "B139",
       "F282", "F242", "B61", "B278", "F180", "E135", "I76", "B287", "E152", "G293",
       "F188", "G120", "E291", "D219", "I18", "I125", "B242", "D218", "D254", "G317",
       "E93", "D300", "I210", "E192", "I45", "E34", "I248", "F99", "F197", "B63",
       "G82", "I109", "D192", "G267", "I197", "E160", "G205", "I14", "D100", "B129",
       "I34", "D268", "B24", "D262", "G175", "E47", "G218", "D233", "I185", "D318",
       "B235", "D315", "B280", "G111", "G312", "G222", "D211", "F23", "I206", "E154",
       "D89", "G225", "E179", "F256", "B267", "E202", "B84", "F130", "G282", "B297",
       "E228", "D65", "F263", "D155", "B54", "D261", "D162", "B80", "I315", "E282",
       "E264", "F272", "D236", "G239", "F310", "I136", "B18", "D142", "B46", "E84",
       "I71", "F50", "D38", "E180", "F143", "G156", "I281", "D279", "F206", "E249",
       "D77", "F30", "G59", "F246", "F120", "I303", "E293", "F138", "B173", "G152",
       "E126", "G99", "I63", "D314", "B152", "G228", "F37", "D63", "G271", "D41",
       "B75", "I20", "I314", "G113", "I82", "I235", "E18", "G129", "D312", "B319",
       "G76", "E212", "D152", "F31", "E119", "G318", "F124", "D208", "E310", "E45",
       "F195", "B241", "I173", "I123", "G187", "F63", "E111", "F154", "D278", "B234",
       "F60", "E23", "D188", "E143", "F241", "B145", "D217", "B216", "G196", "E236",
       "I212", "D111", "I207", "I146", "D148", "B76", "I61", "G181", "I142", "B271",
       "I302", "E106", "E207", "B111", "I97", "D136", "F162", "D161", "F281", "D177",
       "D119", "G300", "B34", "I115", "D109", "D282", "D176", "B45", "G277", "D31",
       "G70", "E247", "I77", "G30", "B20", "F68", "F202", "B181", "D204", "F285",
       "G304", "I95", "D237", "E140", "I154", "E242", "E53", "G91", "B227", "F265",
       "D145", "F140", "D195", "D18", "I68", "E173", "E82", "D205", "E187", "G319",
       "G192", "I218", "F148", "F47", "E137", "D93", "F157", "F141", "D270", "F250",
       "D160", "E265", "B161", "G45", "G138", "D68", "G216", "I55", "B294", "E174",
       "I292", "G33", "B122", "E237", "D123", "I270", "D146", "E268", "D17", "B248",
       "D135", "G160", "G104", "F267", "F279", "D153", "B303", "F223", "B299", "B123",
       "F89", "G249", "G85", "I280", "B82", "D137", "E185", "E36", "B97", "G80",
       "F194", "B142", "D19", "B156", "I119", "B175", "G227", "I191", "D114", "F29",
       "E206", "G270", "G46", "E122", "F97", "G64", "B65", "G194", "D22", "B38",
       "D78", "F66", "D120", "I134", "I299", "B68", "E186", "I66", "I293", "D16",
       "I79", "I254", "I225", "I135", "F34", "D33", "B157", "E295", "G204", "I192",
       "G162", "I108", "B141", "D296", "G31", "D246", "E41", "G84", "G291", "G54",
       "F28", "D99", "B146", "E120", "I204", "F179", "G219", "B206", "G210", "I29",
       "G115", "B99", "E309", "F261", "B77", "D28", "D174", "E241", "G305", "I16",
       "D272", "B114", "E61", "F158", "G303", "I64", "F53", "B272", "G148", "I23",
       "I317", "B177", "G48", "I161", "G232", "B313", "I171", "B266", "F193", "D249",
       "D45", "I111", "B134", "D181", "D14", "F302", "E91", "I65", "B188", "E266",
       "D108", "I126", "E246", "I128", "I40", "D36", "G34", "E70", "E303", "B221",
       "I262", "F173", "D122", "I70", "G198", "B120", "G63", "I236", "B85", "B104",
       "D51", "F45", "I261", "I155", "I239", "E286", "F191", "I318", "E194", "F227",
       "D95", "F187", "F311", "D171", "B95", "B218", "I187", "B17", "D154", "E99",
       "F82", "B154", "D303", "B318", "F239", "B130", "B15", "E262", "F54", "F15",
       "E158", "G105", "I282", "B121", "B282", "E64", "D47", "B193", "F136", "G19",
       "F153", "F51", "E147", "I232", "D222", "G302", "B137", "D194", "G130", "G17",
       "E261", "D223", "E89", "E267", "F210", "B208", "G186", "F217", "F78", "G254",
       "F280", "E136", "F236", "G119", "D138", "E222", "E22", "E175", "B171", "B29",
       "G265", "D191", "G279", "G24", "I194", "B36", "G223", "F61", "F20", "D121",
       "D227", "F262", "G212", "G157", "D232", "I92", "D134", "I221", "I78", "I145",
       "B126", "E161", "I158", "F41", "I41", "G15", "F75", "I234", "F70", "D179",
       "B107", "I124", "G262", "F313", "I285", "E218", "B22", "G114", "E188", "B155",
       "I176", "E80", "B35", "B92", "D264", "F105", "I311", "F181", "I272", "F249",
       "G161", "I283", "E234", "B263", "F95", "D126", "F146", "D49", "I156", "D292",
       "E130", "E19", "B109", "G108", "G100", "B315", "G294", "B53", "B239", "F107",
       "I120", "F248", "E318", "F156", "I160", "E315", "D193", "F219", "B249", "I267",
       "E85", "B89", "F319", "G65", "E311", "D104", "I152", "D157", "I237", "F84",
       "B40", "G310", "E138", "D115", "I249", "G256", "B115", "I233", "D196", "E210",
       "E312", "G295", "G75", "F110", "E148", "E124", "E157", "G126", "D24", "E298",
       "I291", "D266", "B223", "B237", "D276", "B100", "B198", "F62", "I188", "I217",
       "G146", "E197", "F161", "B64", "F137", "I80", "D147", "G106", "B257", "G257",
       "D309", "E297", "E153", "F104", "G92", "D228", "I276", "G208", "I91", "F312",
       "G278", "B207", "B250", "G281", "F14", "G49", "G241", "I93", "B279", "E283",
       "F198", "G313", "G268", "D241", "G272", "F46", "F123", "F40", "F65", "F128",
       "E176", "B195", "G203", "G235", "E142", "F271", "I313", "F24", "D59", "E97",
       "B256", "I174", "E172", "I287", "G172", "F305", "E63", "G158", "F90", "B119",
       "G125", "E257", "D185", "E204", "D225", "B233", "G250", "I195", "I139", "E278",
       "B147", "E302", "F233", "I181", "E305", "E129", "I305", "F135", "F77", "F300",
       "G197", "G207", "B180", "D15", "E105", "F277", "D29", "F35", "G55", "F176",
       "E71", "I193", "F204", "E38", "F225", "B60", "B276", "E108", "F125", "E114",
       "I222", "D235", "D283", "F234", "E55", "G234", "E33", "B91", "D66", "D125",
       "B217", "D97", "I190", "G38", "F106", "I31", "I227", "D281", "B194", "B108",
       "B225", "E270", "D197", "G29", "I205", "G40", "I246", "B228", "F119", "G53",
       "I114", "E95", "G154", "G109", "G190", "D106", "I265", "B110", "E20", "D304",
       "F126", "D61", "D248", "B49", "B247", "D40", "B262", "I296", "I279", "E79",
       "E125", "I266", "F59", "E76", "I148", "I319", "F19", "B143", "G206", "G61",
       "I179", "E233", "E162", "G140", "D311", "E35", "B158", "G171", "G211", "B285",
       "I286", "B14", "I85", "F294", "I129", "G95", "F171", "D50", "E146", "G264",
       "F270", "I304", "E123", "I250", "F129", "D30", "B211", "D190", "F190", "F93",
       "B304", "B277", "I309", "E104", "G18", "F296", "E139", "I177", "D203", "B19",
       "G16", "I175", "I241", "F298", "G217", "D48", "G78", "I223", "F85", "B30",
       "I51", "B196", "G299", "E223", "G145", "G185", "B135", "G155", "I38", "F222",
       "B190", "E287", "E313", "E40", "D280", "I271", "E62", "D37", "B176", "B305",
       "I310", "E279", "I19", "D247", "E59", "F100", "E181", "E281", "B222", "D198",
       "F266", "B205", "B293", "D107", "I247", "G68", "F17", "E190", "G35", "G309",
       "G89", "G315", "E49", "E277", "I62", "I203", "I147", "E46", "F92", "D70",
       "B202", "E193", "D143", "B16", "B79", "D221", "D85", "E100", "G110", "E205",
       "D305", "E272", "E54", "E171", "B296", "I22", "G237", "I53", "F155", "D76",
       "D187", "F221", "D277", "E92", "B125", "F175", "I121", "D180", "D84", "F147",
       "D35", "E128", "B105", "E304", "I59", "G297", "F18", "D256", "D263", "G220",
       "E37", "G153", "E17", "G147", "G221", "E203"]

    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Geographical", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index, value} when value in  [0, "0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Geographical", index, "int", 0)

      {index, value} when  is_integer(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Geographical", index, "int", value)


      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Geographical", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Geographical", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def totals(file_id, data) do
    data.total
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["H19", "I35", "G41", "J86", "E39", "J85", "G20", "G51", "D75", "F86", "G23",
       "G37", "H54", "I39", "G36", "H52", "G50", "I75", "D88", "K86", "F44", "J83",
       "K78", "H37", "H65", "G42", "J84", "I36", "H75", "H26", "F38", "H66", "B37",
       "H23", "G39", "I73", "G77", "H83", "E44", "D86", "K43", "L83", "I13", "L86",
       "J39", "F64", "F76", "B41", "H36", "G86", "J40", "H64", "H38", "G14", "D73",
       "D67", "L45", "B39", "K77", "K40", "I44", "I87", "K76", "H76", "H45", "G22",
       "C39", "D74", "L84", "H21", "H50", "G67", "G83", "G44", "I37", "L73", "H74",
       "J36", "C36", "H39", "F36", "J41", "C43", "I84", "G66", "F74", "F39", "D64",
       "F67", "J35", "H22", "L88", "I76", "J45", "L41", "I45", "H24", "E42", "L38",
       "J87", "G12", "L76", "D65", "I42", "L43", "B44", "K41", "L40", "H42", "H88",
       "D77", "F37", "K88", "I43", "G76", "J88", "E45", "K37", "H55", "J43", "H73",
       "B45", "J42", "I77", "F68", "G43", "H87", "I68", "C45", "L37", "C40", "G45",
       "D68", "D83", "I86", "C35", "H51", "G85", "E36", "K83", "G64", "B38", "D78",
       "G13", "F66", "I66", "H41", "G52", "E41", "G84", "G54", "D87", "F88", "L36",
       "I10", "G11", "F43", "G21", "H67", "I64", "K35", "J44", "H78", "I12", "I65",
       "F73", "I40", "K85", "G88", "F45", "J75", "I74", "G19", "K36", "H43", "K73",
       "F78", "K39", "C44", "C42", "G24", "B36", "K74", "L39", "K45", "I78", "F41",
       "K38", "I41", "F75", "J76", "G73", "L42", "B35", "H35", "I83", "H20", "C41",
       "G65", "G87", "B42", "F84", "B40", "H86", "L74", "G75", "H53", "L44", "E43",
       "H84", "H44", "B43", "F40", "F65", "J37", "C38", "I11", "F77", "J74", "F35",
       "G55", "E38", "C37", "J73", "J78", "K84", "F42", "D66", "G38", "H85", "K75",
       "G74", "G40", "G53", "J38", "K42", "H77", "E35", "G26", "I85", "L75", "L78",
       "L85", "G78", "F85", "I38", "H40", "G10", "K87", "H68", "E40", "I88", "L87",
       "L77", "I67", "G68", "L35", "G35", "F87", "D85", "J77", "D76", "F83", "D84",
       "K44", "E37"]

    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->

        if(index in [
          "E35", "E36", "E37", "E38", "E39", "E40", "E41", "E42", "E43", "E44", "E45",
          "F35", "F36", "F37", "F38", "F39", "F40", "F41", "F42", "F43", "F44", "F45",
          "I35", "I36", "I37", "I38", "I39", "I40", "I41", "I42", "I43", "I44", "I45",
          "J35", "J36", "J37", "J38", "J39", "J40", "J41", "J42", "J43", "J44", "J45",
          "D78", "F78", "G78", "H78", "I78", "J78", "K78", "L78",
          "D88", "F88", "G88", "H88", "I88", "J88", "K88", "L88"
          ],
          do:
            Excelizer.Cell.set_cell_value(file_id, "Totals", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.div(Decimal.new("100")) |> Decimal.to_float()),
          else:
            Excelizer.Cell.set_cell_value(file_id, "Totals", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()),
          )

      {index, value} when value in  [0, "0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Totals", index, "int", 0)

      {index, value} when  is_integer(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Totals", index, "int", value)


      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Totals", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Totals", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  defp string_to_decimal(string) do
    String.replace(string || "0", ",", "") |> Decimal.new()
  end

end
