defmodule MisReports.Mappings do
  @moduledoc """
  The Mappings context. MisReports.Mappings.get_file_specs("cust_segmt")
  """

  import Ecto.Query, warn: false
  alias MisReports.Repo

  alias MisReports.Mappings.FileSpec
  alias MisReports.Mappings.GlMapping
  alias MisReports.SourceData.TrialBalance
  @doc """
  Returns the list of list_file_specs.

  ## Examples

      iex> list_file_specs()
      [%FileSpec{}, ...]

  """
  def change_gl_mapping(%GlMapping{} = gl_mapping, attrs \\ %{}) do
    GlMapping.changeset(gl_mapping, attrs)
  end

  def list_file_specs do
    Repo.all(FileSpec)
  end

  def list_gl_mapping do
    GlMapping
    |> Repo.all()
  end

  def get_gl_mapping_params do
    case Cachex.get(:app_prereqs, "gl_mapping") do
      {:ok, nil} ->
        params = list_gl_mapping()
        Cachex.put(:app_prereqs, "gl_mapping", params, ttl: :timer.minutes(30))
        params

      {:ok, params} ->
        params
    end
  end

  def get_gl(report_type) do
     get_gl_mapping_params()
      |> Enum.filter(fn data -> data.report_type == report_type and data.auth_status == "A" end)
      |> Enum.map(fn i-> Poison.decode!(i.gl_code) end)
      |> List.flatten()
      |> Enum.map(fn i-> i["gl_code"] end)
  end

  def sap_loans_advances(date) do
    gls  = get_gl("NET_LOANS_ADVANCES")
    TrialBalance
    |> where([a], a.date == ^date and a.gl_no in ^gls)
    |> select([a], sum(a.actual_this_year))
    |> Repo.one()
    |> case do
      nil -> Decimal.new("0")
      data -> data |> Decimal.div(Decimal.new("1000"))
    end
  end

  def map do
    data = %{
      "G10" => [
        %{"col_index" => "G10", "col_name" => "162600"},
        %{"col_index" => "G10", "col_name" => "150000"}
      ],
      "G12" => [%{"col_index" => "G12", "col_name" => "150000"}]
    }
    result = data
    |> Map.values()
    |> Enum.flat_map(fn values ->
      Enum.map(values, fn %{"col_index" => col_index, "col_name" => col_name} ->
        %{"col_index" => col_index, "col_name" => col_name}
      end)
    end)
    result
  end

  def insert_gl_mappings do
   data = [
    "499025", "499035"
    ]

    gl_code = %{
      report_type: "TAX",
      maker_id: 1,
      gl_code: Enum.map(data, fn i-> %{gl_code: i} end) |> Poison.encode!()
    }

      GlMapping.changeset(%GlMapping{}, gl_code)
      |> Repo.insert()
  end

  def list_specs(params) do
    FileSpec
    |> isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter(query, nil), do: query
  defp isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.temp_name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.read_line, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.checker_dt, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.auth_status, ^search_term))
  end

  def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"

  @doc """
  Gets a single file_spec.

  Raises `Ecto.NoResultsError` if the FileSpec does not exist.

  ## Examples

      iex> get_file_spec!(123)
      %FileSpec{}

      iex> get_file_spec!(456)
      ** (Ecto.NoResultsError)

  """
  def get_file_spec!(id), do: Repo.get!(FileSpec, id)

  def file_spec_lookup(id) do
    FileSpec
    |> where(id: ^id)
    |> preload([:file_spec_cols])
    |> Repo.one
  end

  @doc """
  Creates a file_spec.

  ## Examples

      iex> create_file_spec(%{field: value})
      {:ok, %FileSpec{}}

      iex> create_file_spec(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_file_spec(attrs \\ %{}) do
    %FileSpec{}
    |> FileSpec.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a file_spec.

  ## Examples

      iex> update_file_spec(file_spec, %{field: new_value})
      {:ok, %FileSpec{}}

      iex> update_file_spec(file_spec, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_file_spec(%FileSpec{} = file_spec, attrs) do
    file_spec
    |> FileSpec.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a file_spec.

  ## Examples

      iex> delete_file_spec(file_spec)
      {:ok, %FileSpec{}}

      iex> delete_file_spec(file_spec)
      {:error, %Ecto.Changeset{}}

  """
  def delete_file_spec(%FileSpec{} = file_spec) do
    Repo.delete(file_spec)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking file_spec changes.

  ## Examples

      iex> change_file_spec(file_spec)
      %Ecto.Changeset{data: %FileSpec{}}

  """
  def change_file_spec(%FileSpec{} = file_spec, attrs \\ %{}) do
    FileSpec.changeset(file_spec, attrs)
  end

  def get_file_specs(spec_tag) do
    query =
      from s in FileSpec,
        where: s.temp_name == ^spec_tag and s.auth_status == "A",
        join: i in assoc(s, :file_spec_cols),
        select: {s, i}

    Repo.all(query)
    |> Enum.group_by(fn {file_spec, _} -> file_spec.id end)
    |> Enum.map(fn {_key, values} ->
      file_spec = hd(values) |> elem(0)

      file_spec_columns =
        values
        |> Enum.map(fn {_, columns} ->
          {columns.col_name, columns.col_index}
        end)

      {file_spec.read_line, file_spec_columns}
    end)
    |> case do
      [] -> []
      [inner] -> inner
      result -> result
    end
  end

  alias MisReports.Mappings.TrialBalMapping

  @doc """
  Returns the list of trial_bal_mapping.

  ## Examples

      iex> list_trial_bal_mapping()
      [%TrialBalMapping{}, ...]

  """
  def list_trial_bal_mapping do
    Repo.all(TrialBalMapping)
  end

  @doc """
  Gets a single trial_bal_mapping.

  Raises `Ecto.NoResultsError` if the Trial bal mapping does not exist.

  ## Examples

      iex> get_trial_bal_mapping!(123)
      %TrialBalMapping{}

      iex> get_trial_bal_mapping!(456)
      ** (Ecto.NoResultsError)

  """
  def get_trial_bal_mapping!(id), do: Repo.get!(TrialBalMapping, id)

  @doc """
  Creates a trial_bal_mapping.

  ## Examples

      iex> create_trial_bal_mapping(%{field: value})
      {:ok, %TrialBalMapping{}}

      iex> create_trial_bal_mapping(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_trial_bal_mapping(attrs \\ %{}) do
    %TrialBalMapping{}
    |> TrialBalMapping.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a trial_bal_mapping.

  ## Examples

      iex> update_trial_bal_mapping(trial_bal_mapping, %{field: new_value})
      {:ok, %TrialBalMapping{}}

      iex> update_trial_bal_mapping(trial_bal_mapping, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_trial_bal_mapping(%TrialBalMapping{} = trial_bal_mapping, attrs) do
    trial_bal_mapping
    |> TrialBalMapping.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a trial_bal_mapping.

  ## Examples

      iex> delete_trial_bal_mapping(trial_bal_mapping)
      {:ok, %TrialBalMapping{}}

      iex> delete_trial_bal_mapping(trial_bal_mapping)
      {:error, %Ecto.Changeset{}}

  """
  def delete_trial_bal_mapping(%TrialBalMapping{} = trial_bal_mapping) do
    Repo.delete(trial_bal_mapping)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking trial_bal_mapping changes.

  ## Examples

      iex> change_trial_bal_mapping(trial_bal_mapping)
      %Ecto.Changeset{data: %TrialBalMapping{}}

  """
  def change_trial_bal_mapping(%TrialBalMapping{} = trial_bal_mapping, attrs \\ %{}) do
    TrialBalMapping.changeset(trial_bal_mapping, attrs)
  end

  alias MisReports.Mappings.FileSpecColumns

  @doc """
  Returns the list of tbl_file_spec_columns.

  ## Examples

      iex> list_tbl_file_spec_columns()
      [%FileSpecColumns{}, ...]

  """
  def list_tbl_file_spec_columns do
    Repo.all(FileSpecColumns)
  end

  @doc """
  Gets a single file_spec_columns.

  Raises `Ecto.NoResultsError` if the File spec columns does not exist.

  ## Examples

      iex> get_file_spec_columns!(123)
      %FileSpecColumns{}

      iex> get_file_spec_columns!(456)
      ** (Ecto.NoResultsError)

  """
  def get_file_spec_columns!(id), do: Repo.get!(FileSpecColumns, id)

  def get_file_spec_columns(file_spec_id, col_name) do
    FileSpecColumns
    |> where(file_spec_id: ^file_spec_id, col_name: ^col_name)
    |> Repo.one
  end

  @doc """
  Creates a file_spec_columns.

  ## Examples

      iex> create_file_spec_columns(%{field: value})
      {:ok, %FileSpecColumns{}}

      iex> create_file_spec_columns(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_file_spec_columns(attrs \\ %{}) do
    %FileSpecColumns{}
    |> FileSpecColumns.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a file_spec_columns.

  ## Examples

      iex> update_file_spec_columns(file_spec_columns, %{field: new_value})
      {:ok, %FileSpecColumns{}}

      iex> update_file_spec_columns(file_spec_columns, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_file_spec_columns(%FileSpecColumns{} = file_spec_columns, attrs) do
    file_spec_columns
    |> FileSpecColumns.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a file_spec_columns.

  ## Examples

      iex> delete_file_spec_columns(file_spec_columns)
      {:ok, %FileSpecColumns{}}

      iex> delete_file_spec_columns(file_spec_columns)
      {:error, %Ecto.Changeset{}}

  """
  def delete_file_spec_columns(%FileSpecColumns{} = file_spec_columns) do
    Repo.delete(file_spec_columns)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking file_spec_columns changes.

  ## Examples

      iex> change_file_spec_columns(file_spec_columns)
      %Ecto.Changeset{data: %FileSpecColumns{}}

  """
  def change_file_spec_columns(%FileSpecColumns{} = file_spec_columns, attrs \\ %{}) do
    FileSpecColumns.changeset(file_spec_columns, attrs)
  end

  def file_spec_cols_lookup(id) do
    FileSpecColumns
    |> where(file_spec_id: ^id)
  end

  alias MisReports.Mappings.BankAccount

  @doc """
  Returns the list of bank_accounts.

  ## Examples

      iex> list_bank_accounts()
      [%BankAccount{}, ...]

  """
  def list_bank_accounts do
    Repo.all(BankAccount)
  end

  def list_gl_mappings do
    Repo.all(GlMapping)
  end
  def get_gl_mapping!(id), do: Repo.get!(GlMapping, id)
  def authorized_accounts,
      do:
      Repo.all(from(a in BankAccount,where: a.status == "A", select: {a.acc_no, a.acc_name}))
      |> Enum.map(fn {acc_no, acc_name} -> {"#{acc_no} #{acc_name}", acc_no} end)
      |> Enum.sort_by(&(&1))

  def gl_list(value) do
   data = Repo.all(from(a in BankAccount, where: a.status == "A", select: {a.acc_no, a.acc_name}))
          |> Enum.map(fn {acc_no, acc_name} -> {acc_no, "#{acc_no} #{acc_name}"} end)
          |> Enum.into(%{})
    data[value]
  end


  def list_bank_accounts(params) do
    BankAccount
    |> isearch_bank_acc_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end


  defp isearch_bank_acc_filter(query, nil), do: query

  defp isearch_bank_acc_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.acc_no, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.acc_name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
  end

  def list_gl_mappings(params) do
    GlMapping
    |> isearch_gl_mapping_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_gl_mapping_filter(query, nil), do: query

  defp isearch_gl_mapping_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.gl_names, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.schedule, ^search_term))
  end

  def mapping_keys(value) do
   data =  %{
              "LOAN_ADVANCES_NORMAL_DEPOSITS" => "Loans and advances from normal deposits (excluding leasing income)",
              "LOAN_ADVANCES_DEDICATED_LINES" => "Loans and advances from dedicated lines of credit (including leasing income)",
              "FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS" => "From banks and other financial institutions (including OMO deposits)",
              "SECURITIES_TREASURY_BILLS" => "Securities Treasury Bills",
              "SECURITIES_GOVERNMENT_BONDS" => "Securities Government Bonds",
              "LEASING_INCOME_FROM_NORMAL_DEPOSITS" => "Leasing Income from normal deposits",
              "CREDIT_CARDS" => "Credit Cards",
              "ALL_OTHER" => "All Other",
              "DEPOSITS_FOREIGN_LOCAL_CURRENCY_DEMAND" => "Deposits Foreign/Local Currency Demand",
              "DEPOSITS_FOREIGN_LOCAL_CURRENCY_SAVINGS" => "Deposits Foreign/Local Currency Savings",
              "TIME" => "Time",
              "INTEREST_TO_BANKS" => "Interest Paid to Banks and Financial Institutions",
              "DEPOSITS_FOREIGN_LOCAL" => "Deposits Foreign/Local",
              "COMMISION_FEES" => "Commissions, fees and service charges (local currency transactions)",
              "FEES_FROM_FOREIGN_EXCHANGE" => "Fees From Foreign Exchange",
              "FOREIGN_EXCHANGE_UNREALISED_GAINS" => "Foreign Exchange Unrealised gains/ (losses) from foreign exchange holdings",
              "FOREIGN_EXCHANGE_REALISED_GAINS" => "Foreign Exchange Realised gains/ (losses) from foreign exchange holdings",
              "TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES" => "Trading Income Realised trading gains / (losses)",
              "TRADING_INCOME_UNREALIZED_TRADING_GAINS_LOSSES" => "Trading Income Unrealised trading gains / (losses)",
              "TRADING_INCOME" => "Trading Income",
              "OCCUPANCY" => "Occupancy",
              "EQUIPMENT" => "Equipment",
              "SUBORDINATED_DEBT" => "Subordinated debt",
              "DEPRECIATION" => "Depreciation",
              "EDUCATION_AND_TRAINING" => "Education and Training",
              "AUDIT_LEGAL_AND_PROFESSIONAL_FEES" => "Audit, Legal and Professional Fees",
              "INSURANCE" => "Insurance",
              "MANAGEMENT_FEES" => "Management Fees",
              "DONATIONS" => "Donations",
              "NOTES_AND_COINS" => "Notes And Coins",
              "BALANCES_WITH_BANK_OF_ZAMBIA_STATUTORY" => "Balances With Bank Of Zambia Statutory",
              "BALANCES_WITH_BANK_OF_ZAMBIA_CURRENT_ACCOUNT" => "Balances With Bank Of Zambia Current Account",
              "PREPAID_AND_DEFERRED_CHARGES" => "Prepaid and deferred charges",
              "GOODWILL_AND_OTHER_INTANGIBLES" => "Goodwill and other intangibles",
              "DEFERRED_INCOME" => "Deferred Income",
              "LOAN_ALLOWANCES" => "Allowance For Loan Losses",
              "NET_LOANS_ADVANCES" => "Net Loans And Advances",
              "CUR_BALANCES_WITH_FOREIGN_INSTITUTIONS_STG_1" => "Current Balances with Foreign Institutions Stage 1",
              "CUR_BALANCES_WITH_FOREIGN_INSTITUTIONS_STG_2" => "Current Balances with Foreign Institutions Stage 2",
              "CUR_BALANCES_WITH_FOREIGN_INSTITUTIONS_STG_3" => "Current Balances with Foreign Institutions Stage 3",
              "PRE_BALANCES_WITH_FOREIGN_INSTITUTIONS_STG_1" => "Previous Balances with Foreign Institutions Stage 1",
              "PRE_BALANCES_WITH_FOREIGN_INSTITUTIONS_STG_2" => "Previous Balances with Foreign Institutions Stage 2",
              "PRE_BALANCES_WITH_FOREIGN_INSTITUTIONS_STG_3" => "Previous Balances with Foreign Institutions Stage 3",
              "CUR_INVESTMENTS_GOVERNMENT_SECURITIES_STG_1" => "Current Investments in Government Securities Stage 1",
              "CUR_INVESTMENTS_GOVERNMENT_SECURITIES_STG_2" => "Current Investments in Government Securities Stage 2",
              "CUR_INVESTMENTS_GOVERNMENT_SECURITIES_STG_3" => "Current Investments in Government Securities Stage 3",
              "PRE_INVESTMENTS_GOVERNMENT_SECURITIES_STG_1" => "Previous Investments in Government Securities Stage 1",
              "PRE_INVESTMENTS_GOVERNMENT_SECURITIES_STG_2" => "Previous Investments in Government Securities Stage 2",
              "PRE_INVESTMENTS_GOVERNMENT_SECURITIES_STG_3" => "Previous Investments in Government Securities Stage 3",
              "CUR_OTHERS_STG_1" => "Current Others Stage 1",
              "CUR_OTHERS_STG_2" => "Current Others Stage 2",
              "CUR_OTHERS_STG_3" => "Current Others Stage 3",
              "PRE_OTHERS_STG_1" => "Previous Others Stage 1",
              "PRE_OTHERS_STG_2" => "Previous Others Stage 2",
              "PRE_OTHERS_STG_3" => "Previous Others Stage 3",
              "CUR_OFF_BALANCE_SHEET_EXPOSURES_STG_1" => "Current Off-balance sheet exposures Stage 1",
              "CUR_OFF_BALANCE_SHEET_EXPOSURES_STG_2" => "Current Off-balance sheet exposures Stage 2",
              "CUR_OFF_BALANCE_SHEET_EXPOSURES_STG_3" => "Current Off-balance sheet exposures Stage 3",
              "PRE_OFF_BALANCE_SHEET_EXPOSURES_STG_1" => "Previous Off-balance sheet exposures Stage 1",
              "PRE_OFF_BALANCE_SHEET_EXPOSURES_STG_2" => "Previous Off-balance sheet exposures Stage 2",
              "PRE_OFF_BALANCE_SHEET_EXPOSURES_STG_3" => "Previous Off-balance sheet exposures Stage 3",
              "MIS_OCCUPANCY_COSTS" => "Mis Occupancy Costs",
              "MIS_EQUIPMENT_COSTS" => "Mis Equipment Costs",
              "MIS_STAFF_COSTS" => "Mis Staff Costs",
              "MIS_DEPRICATION_COSTS" => "Mis Depreciation Costs",
              "MIS_TRAINING_COSTS" => "Mis Training Costs",
              "MIS_PROFESSIONAL_COSTS" => "Mis Audit, Legal and Professional fees",
              "MIS_INSURANCE_COSTS" => "Mis Insurance Costs",
              "MIS_MANAGEMENT_COSTS" => "Mis Management Costs",
              "MIS_DONATIONS_COSTS" => "Mis Donations Costs",
              "MIS_SUBSCRIPTION_COSTS" => "Mis Subscriptions Costs",
              "MIS_COMMISSION_COSTS" => "Mis Commisions Costs",
              "MIS_SECURITY_COSTS" => "Mis Security Costs",
              "MIS_INTANGIBLE_COSTS" => "Mis Intangible amortisation costs",
              "MIS_SOFTWARE_COSTS" => "Mis Software Costs",
              "MIS_COMMUNICATION_COSTS" => "Mis Communication Costs",
              "MIS_ENTERTAINMENT_COSTS" => "Mis Entertainment Costs",
              "MIS_PROCESSING_COSTS" => "Mis Processing Costs",
              "MIS_STATIONARY_COSTS" => "Mis Stationary Costs",
              "MIS_MARKETING_COSTS" => "Mis Marketing Costs",
              "MIS_OPERATIONAL_COSTS" => "Mis Operational Losses",
              "MIS_MISCELLENEOUS_COSTS" => "Mis Miscelleanous Losses",
              "MIS_BANK_COSTS" => "Mis Bank Charges Costs",
              "MIS_VEHICLE_COSTS" => "Mis Vehicle Costs",
              "MIS_DIRECTORS_COSTS" => "Mis Directors Costs",
              "MIS_INTERCOMPANY_COSTS" => "Mis Intercompany Costs",
              "MIS_INDIRECT_COSTS" => "Mis Indirect Costs",
              "M305053_Interest_Income_treasury_bills" => "M305053 Interest Income from treasury bills",
              "M305054_INTEREST_INCOME_GOVT_BONDS" => "M305054  Interest income from Govt Bonds",
              "ALL_OTHER_INTEREST_EXPENSES" => "All Other Interest Expenses",
              "GENERAL_DEBT_PROVISIONING_PRUDENTIAL" => "General Debt Provisioning Prudential",
              "M313534_STAGE_3_SDP_IMPAIRMENT_CHARGES" => "M313534  Stage 3 SDP Impairment charges",
              "TOTAL_OTHER_PROVISIONS_CHARGE_IN_PRUDENTIAL" => "Total Other Provisions Charge in Prudential",
              "ID_TRADING_INCOME" => "ID Trading Income",
              "ID_INTEREST_RECEIVED_TRANSFERED_TO_TRADING_INCOME" => "ID Interest Received Transfered to Trading Income",
              "ID_INTEREST_EXPENSE_TRANSFERED_TO_TRADING_INCOME" => "ID Interest Expense Transfered to Trading Income",
              "FX_GAIN_LOSS" => "FX Gain/Loss",
              "SUNDRY_INCOME" => "Sundry Income",
              "SUNDRY_INCOME_OTHER" => "Sundry Income Other",
              "RENTAL_INCOME" => "Rental Income",
              "FX_GAIN_LOSS_2" => "FX Gain/Loss other",
              "INCOME_FROM_DISPOSAL_OF_ASSETS" => "Income from disposal of assets",
              "TOTAL_WRITE_BACKS_TRANSFERRED" => "Total write backs transferred to other income",
              "BASIC_PAY" => "Basic Pay",
              "ALLOWANCE" => "Allowance",
              "BONUSES" => "Bonuses",
              "PENSION" => "Pension",
              "NAPSA" => "NAPSA",
              "OVERTIME_PAY" => "Overtime Pay",
              "OTHER_BENEFITS" => "Other Benefits",
              "M332220_ADMINISTRATION_AND_MEMBERSHIP_FEES" => "M332220 Administration and Membership Fees",
              "M332620_COMMISSION_PAID" => "M332620 Commission Paid",
              "M332720_SECURITY_EXPENSES" => "M332720 Security Expenses",
              "M329700_AMORTIZATION_OF_INTANGIBLES" => "M329700 Amortization of Intangibles",
              "M340070_SOFTWARE_MAINTENANCE" => "M340070 Software Maintenance",
              "M340120_COMMUNICATION_EXPENSES" => "M340120 Communication Expenses",
              "M340200_TRAVEL_ENTERTAINMENT" => "M340200 Travel and Entertainment",
              "M340320_PROCESSING_COSTS" => "M340320 Processing Costs",
              "M340420_STATIONARY_PRINTING" => "M340420 Stationary & Printing",
              "M340520_MARKETING_AND_ADVERTISING" => "M340520 Marketing & Advertising",
              "M340600_OPERATIONAL_RISK_LOSSES" => "M340600 Operational Risk Losses",
              "M300701_MISCELLANEOUS_COSTS" => "M300701 Miscellaneous Costs",
              "M300716_BANK_CHARGES_PENALTIES_AND_FINES" => "M300716 Bank Charges, Penalties and Fines",
              "M300705_MOTORING_EXPENSES" => "M300705 Motoring Expenses",
              "M300707_DIRECTORS" => "M300707 Directors",
              "M300713_FX_AND_COST_HEDGE" => "M300713 FX and Cost Hedge",
              "M340800_IC_OPERATING_EXPENSES" => "M340800 IC Operating Expenses",
              "M353000_INDIRECT_TAXES" => "M353000 Indirect Taxes",
              "TAXES" => "Tax",

            }
    data[value]
  end

  @doc """
  Gets a single bank_account.

  Raises `Ecto.NoResultsError` if the Bank account does not exist.

  ## Examples

      iex> get_bank_account!(123)
      %BankAccount{}

      iex> get_bank_account!(456)
      ** (Ecto.NoResultsError)

  """
  def get_bank_account!(id), do: Repo.get!(BankAccount, id)

  @doc """
  Creates a bank_account.

  ## Examples

      iex> create_bank_account(%{field: value})
      {:ok, %BankAccount{}}

      iex> create_bank_account(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_bank_account(attrs \\ %{}) do
    %BankAccount{}
    |> BankAccount.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a bank_account.

  ## Examples

      iex> update_bank_account(bank_account, %{field: new_value})
      {:ok, %BankAccount{}}

      iex> update_bank_account(bank_account, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_bank_account(%BankAccount{} = bank_account, attrs) do
    bank_account
    |> BankAccount.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a bank_account.

  ## Examples

      iex> delete_bank_account(bank_account)
      {:ok, %BankAccount{}}

      iex> delete_bank_account(bank_account)
      {:error, %Ecto.Changeset{}}

  """
  def delete_bank_account(%BankAccount{} = bank_account) do
    Repo.delete(bank_account)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking bank_account changes.

  ## Examples

      iex> change_bank_account(bank_account)
      %Ecto.Changeset{data: %BankAccount{}}

  """
  def change_bank_account(%BankAccount{} = bank_account, attrs \\ %{}) do
    BankAccount.changeset(bank_account, attrs)
  end

  alias MisReports.Mappings.ScheduleSectionMapping

  @doc """
  Returns the list of schedule_section_mappings.

  ## Examples

      iex> list_schedule_section_mappings()
      [%ScheduleSectionMapping{}, ...]

  """
  def list_schedule_section_mappings do
    Repo.all(ScheduleSectionMapping)
  end

  @doc """
  Gets a single schedule_section_mapping.

  Raises `Ecto.NoResultsError` if the Schedule section mapping does not exist.

  ## Examples

      iex> get_schedule_section_mapping!(123)
      %ScheduleSectionMapping{}

      iex> get_schedule_section_mapping!(456)
      ** (Ecto.NoResultsError)

  """
  def get_schedule_section_mapping!(id), do: Repo.get!(ScheduleSectionMapping, id)

  @doc """
  Creates a schedule_section_mapping.

  ## Examples

      iex> create_schedule_section_mapping(%{field: value})
      {:ok, %ScheduleSectionMapping{}}

      iex> create_schedule_section_mapping(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_schedule_section_mapping(attrs \\ %{}) do
    %ScheduleSectionMapping{}
    |> ScheduleSectionMapping.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a schedule_section_mapping.

  ## Examples

      iex> update_schedule_section_mapping(schedule_section_mapping, %{field: new_value})
      {:ok, %ScheduleSectionMapping{}}

      iex> update_schedule_section_mapping(schedule_section_mapping, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_schedule_section_mapping(%ScheduleSectionMapping{} = schedule_section_mapping, attrs) do
    schedule_section_mapping
    |> ScheduleSectionMapping.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a schedule_section_mapping.

  ## Examples

      iex> delete_schedule_section_mapping(schedule_section_mapping)
      {:ok, %ScheduleSectionMapping{}}

      iex> delete_schedule_section_mapping(schedule_section_mapping)
      {:error, %Ecto.Changeset{}}

  """
  def delete_schedule_section_mapping(%ScheduleSectionMapping{} = schedule_section_mapping) do
    Repo.delete(schedule_section_mapping)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking schedule_section_mapping changes.

  ## Examples

      iex> change_schedule_section_mapping(schedule_section_mapping)
      %Ecto.Changeset{data: %ScheduleSectionMapping{}}

  """
  def change_schedule_section_mapping(%ScheduleSectionMapping{} = schedule_section_mapping, attrs \\ %{}) do
    ScheduleSectionMapping.changeset(schedule_section_mapping, attrs)
  end

end
