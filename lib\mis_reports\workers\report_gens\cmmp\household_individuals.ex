defmodule MisReports.Workers.ReportGens.Cmmp.HouseholdIndividual do
  alias MisReports.Prudentials

  def generate_display(start_date, end_date, _adjustments) do

    data = Prudentials.cmmp_quartely(start_date, end_date)
            |> Enum.map(fn  i->
              Map.merge(i, %{"loan_classification" => if(i["obddr"] == nil, do: "PL", else: "NPL")})
            end)
            |> loan_book_age_analysis_classification()
            |> Enum.filter(fn data ->
                data["business_unit"] == "Personal & Private Banking"
                and data["subsegment_descr_source"] != "PUBLIC SECTOR AND GOVERNMENT"
                and data["economic_sector"] != "Agriculture, forestry and fishing"
              end)


    debtors_book_analysis = MisReports.SourceData.get_debtors_book_analysis_by_date(end_date, "hseholds_indiv")

    start_date = Date.from_iso8601!(start_date)
    end_date = Date.from_iso8601!(end_date)

    quarterly_data = data
                      |> Enum.filter(fn transaction ->
                        date = transaction["account_open_date"]
                        Date.compare(date, start_date) in [:eq, :gt] and Date.compare(date, end_date) in [:eq, :lt]
                      end)


    rural_loans = filter_loans_by_geo_dist(quarterly_data, "Rural")
    urban_loans = filter_loans_by_geo_dist(quarterly_data, "Urban")

    urban_rural_loans = rural_loans ++ urban_loans


    total_rural = sum_loans(rural_loans)
    total_urban = sum_loans(urban_loans)

    house_hold_zmw_value = sum_loans(quarterly_data)

    count = Enum.count(quarterly_data)

    schedule = %{
              "G11" => quarterly_data |> filter_loans_by_debtors_book_analysis("Mortgage credit agreement") |> count_loans(),
              "I11" => "Y",
              "G12" => quarterly_data |> filter_loans_by_debtors_book_analysis("Leases and other asset-backed/secured loans") |> count_loans(),
              "I12" => "Y",
              "G13" => quarterly_data |> filter_loans_by_debtors_book_analysis("Unsecured and Other Loans") |> count_loans(),
              "I13" => "Y",
              "G14" => quarterly_data |> filter_loans_by_debtors_book_analysis("Revolving credit") |> count_loans(),
              "I14" => "Y",
              "G15" => quarterly_data  |> count_loans(),

              "G23" => house_hold_zmw_value,
              "H23" => count,
              "I23" => "Y",
              "G24" => house_hold_zmw_value,
              "H24" => count,

              "G27" => get_loans_by_gender(quarterly_data) |> sum_loans(),
              "H27" => get_loans_by_gender(quarterly_data) |> count_loans(),
              "I27" => "Y",

              "G28" => get_loans_by_age(quarterly_data) |> sum_loans(),
              "H28" => get_loans_by_age(quarterly_data) |> count_loans(),
              "I28" => "Y",
              #=======================================RURAL
              "B33" => sum_loans_by_province(rural_loans, "Lusaka"),
              "C33" => count_loans_by_province(rural_loans, "Lusaka"),
              "E33" => percentage_of_total(sum_loans_by_province(rural_loans, "Lusaka"), total_rural),
              "F33" => percentage_of_total(count_loans_by_province(rural_loans, "Lusaka"), count_loans(rural_loans)),

              "B34" => sum_loans_by_province(rural_loans, "Central"),
              "C34" => count_loans_by_province(rural_loans, "Central"),
              "E34" => percentage_of_total(sum_loans_by_province(rural_loans, "Central"), total_rural),
              "F34" => percentage_of_total(count_loans_by_province(rural_loans, "Central"), count_loans(rural_loans)),

              "B35" => sum_loans_by_province(rural_loans, "Copperbelt"),
              "C35" => count_loans_by_province(rural_loans, "Copperbelt"),
              "E35" => percentage_of_total(sum_loans_by_province(rural_loans, "Copperbelt"), total_rural),
              "F35" => percentage_of_total(count_loans_by_province(rural_loans, "Copperbelt"), count_loans(rural_loans)),

              "B36" => sum_loans_by_province(rural_loans, "Eastern"),
              "C36" => count_loans_by_province(rural_loans, "Eastern"),
              "E36" => percentage_of_total(sum_loans_by_province(rural_loans, "Eastern"), total_rural),
              "F36" => percentage_of_total(count_loans_by_province(rural_loans, "Eastern"), count_loans(rural_loans)),

              "B37" => sum_loans_by_province(rural_loans, "Luapula"),
              "C37" => count_loans_by_province(rural_loans, "Luapula"),
              "E37" => percentage_of_total(sum_loans_by_province(rural_loans, "Luapula"), total_rural),
              "F37" => percentage_of_total(count_loans_by_province(rural_loans, "Luapula"), count_loans(rural_loans)),

              "B38" => sum_loans_by_province(rural_loans, "Muchinga"),
              "C38" => count_loans_by_province(rural_loans, "Muchinga"),
              "E38" => percentage_of_total(sum_loans_by_province(rural_loans, "Muchinga"), total_rural),
              "F38" => percentage_of_total(count_loans_by_province(rural_loans, "Muchinga"), count_loans(rural_loans)),

              "B39" => sum_loans_by_province(rural_loans, "Northern"),
              "C39" => count_loans_by_province(rural_loans, "Northern"),
              "E39" => percentage_of_total(sum_loans_by_province(rural_loans, "Northern"), total_rural),
              "F39" => percentage_of_total(count_loans_by_province(rural_loans, "Northern"), count_loans(rural_loans)),

              "B40" => sum_loans_by_province(rural_loans, "Northwestern"),
              "C40" => count_loans_by_province(rural_loans, "Northwestern"),
              "E40" => percentage_of_total(sum_loans_by_province(rural_loans, "Northwestern"), total_rural),
              "F40" => percentage_of_total(count_loans_by_province(rural_loans, "Northwestern"), count_loans(rural_loans)),

              "B41" => sum_loans_by_province(rural_loans, "Southern"),
              "C41" => count_loans_by_province(rural_loans, "Southern"),
              "E41" => percentage_of_total(sum_loans_by_province(rural_loans, "Southern"), total_rural),
              "F41" => percentage_of_total(count_loans_by_province(rural_loans, "Southern"), count_loans(rural_loans)),

              "B42" => sum_loans_by_province(rural_loans, "Western"),
              "C42" => count_loans_by_province(rural_loans, "Western"),
              "E42" => percentage_of_total(sum_loans_by_province(rural_loans, "Western"), total_rural),
              "F42" => percentage_of_total(count_loans_by_province(rural_loans, "Western"), count_loans(rural_loans)),

              "B43" => sum_loans(rural_loans),
              "C43" => count_loans(rural_loans),
              "E43" => percentage_of_total(sum_loans(rural_loans), total_rural),
              "F43" => percentage_of_total(count_loans(rural_loans), count_loans(rural_loans)),

              #==============================URBAN
              "G33" => sum_loans_by_province(urban_loans, "Lusaka"),
              "H33" => count_loans_by_province(urban_loans, "Lusaka"),
              "I33" => percentage_of_total(sum_loans_by_province(urban_loans, "Lusaka"), total_urban),
              "J33" => percentage_of_total(count_loans_by_province(urban_loans, "Lusaka"), count_loans(urban_loans)),
              "K33" => sum_loans_by_province(urban_rural_loans, "Lusaka"),
              "L33" => count_loans_by_province(urban_rural_loans, "Lusaka"),

              "G34" => sum_loans_by_province(urban_loans, "Central"),
              "H34" => count_loans_by_province(urban_loans, "Central"),
              "I34" => percentage_of_total(sum_loans_by_province(urban_loans, "Central"), total_urban),
              "J34" => percentage_of_total(count_loans_by_province(urban_loans, "Central"), count_loans(urban_loans)),
              "K34" => sum_loans_by_province(urban_rural_loans, "Central"),
              "L34" => count_loans_by_province(urban_rural_loans, "Central"),

              "G35" => sum_loans_by_province(urban_loans, "Copperbelt"),
              "H35" => count_loans_by_province(urban_loans, "Copperbelt"),
              "I35" => percentage_of_total(sum_loans_by_province(urban_loans, "Copperbelt"), total_urban),
              "J35" => percentage_of_total(count_loans_by_province(urban_loans, "Copperbelt"), count_loans(urban_loans)),
              "K35" => sum_loans_by_province(urban_rural_loans, "Copperbelt"),
              "L35" => count_loans_by_province(urban_rural_loans, "Copperbelt"),

              "G36" => sum_loans_by_province(urban_loans, "Eastern"),
              "H36" => count_loans_by_province(urban_loans, "Eastern"),
              "I36" => percentage_of_total(sum_loans_by_province(urban_loans, "Eastern"), total_urban),
              "J36" => percentage_of_total(count_loans_by_province(urban_loans, "Eastern"), count_loans(urban_loans)),
              "K36" => sum_loans_by_province(urban_rural_loans, "Eastern"),
              "L36" => count_loans_by_province(urban_rural_loans, "Eastern"),

              "G37" => sum_loans_by_province(urban_loans, "Luapula"),
              "H37" => count_loans_by_province(urban_loans, "Luapula"),
              "I37" => percentage_of_total(sum_loans_by_province(urban_loans, "Luapula"), total_urban),
              "J37" => percentage_of_total(count_loans_by_province(urban_loans, "Luapula"), count_loans(urban_loans)),
              "K37" => sum_loans_by_province(urban_rural_loans, "Luapula"),
              "L37" => count_loans_by_province(urban_rural_loans, "Luapula"),

              "G38" => sum_loans_by_province(urban_loans, "Muchinga"),
              "H38" => count_loans_by_province(urban_loans, "Muchinga"),
              "I38" => percentage_of_total(sum_loans_by_province(urban_loans, "Muchinga"), total_urban),
              "J38" => percentage_of_total(count_loans_by_province(urban_loans, "Muchinga"), count_loans(urban_loans)),
              "K38" => sum_loans_by_province(urban_rural_loans, "Muchinga"),
              "L38" => count_loans_by_province(urban_rural_loans, "Muchinga"),

              "G39" => sum_loans_by_province(urban_loans, "Northern"),
              "H39" => count_loans_by_province(urban_loans, "Northern"),
              "I39" => percentage_of_total(sum_loans_by_province(urban_loans, "Northern"), total_urban),
              "J39" => percentage_of_total(count_loans_by_province(urban_loans, "Northern"), count_loans(urban_loans)),
              "K39" => sum_loans_by_province(urban_rural_loans, "Northern"),
              "L39" => count_loans_by_province(urban_rural_loans, "Northern"),

              "G40" => sum_loans_by_province(urban_loans, "Northwestern"),
              "H40" => count_loans_by_province(urban_loans, "Northwestern"),
              "I40" => percentage_of_total(sum_loans_by_province(urban_loans, "Northwestern"), total_urban),
              "J40" => percentage_of_total(count_loans_by_province(urban_loans, "Northwestern"), count_loans(urban_loans)),
              "K40" => sum_loans_by_province(urban_rural_loans, "Northwestern"),
              "L40" => count_loans_by_province(urban_rural_loans, "Northwestern"),

              "G41" => sum_loans_by_province(urban_loans, "Southern"),
              "H41" => count_loans_by_province(urban_loans, "Southern"),
              "I41" => percentage_of_total(sum_loans_by_province(urban_loans, "Southern"), total_urban),
              "J41" => percentage_of_total(count_loans_by_province(urban_loans, "Southern"), count_loans(urban_loans)),
              "K41" => sum_loans_by_province(urban_rural_loans, "Southern"),
              "L41" => count_loans_by_province(urban_rural_loans, "Southern"),

              "G42" => sum_loans_by_province(urban_loans, "Western"),
              "H42" => count_loans_by_province(urban_loans, "Western"),
              "I42" => percentage_of_total(sum_loans_by_province(urban_loans, "Western"), total_urban),
              "J42" => percentage_of_total(count_loans_by_province(urban_loans, "Western"), count_loans(urban_loans)),
              "K42" => sum_loans_by_province(urban_rural_loans, "Western"),
              "L42" => count_loans_by_province(urban_rural_loans, "Western"),

              "G43" => sum_loans(urban_loans),
              "H43" => count_loans(urban_loans),
              "I43" => percentage_of_total(sum_loans(urban_loans), total_urban),
              "J43" => percentage_of_total(count_loans(urban_loans), count_loans(urban_loans)),
              "K43" => sum_loans(urban_rural_loans),
              "L43" => count_loans(urban_rural_loans),

              "G49" => filter_loans_by_product(quarterly_data, "Mortgage credit agreement") |> sum_loans(),
              "H49" => filter_loans_by_product(quarterly_data, "Mortgage credit agreement") |> count_loans(),
              "G52" => filter_loans_by_product(quarterly_data, "Mortgage credit agreement") |> sum_loans(),
              "H52" => filter_loans_by_product(quarterly_data, "Mortgage credit agreement") |> count_loans(),
              "G54" => filter_loans_by_product(quarterly_data, "Loans and Leases secured by vehical") |> sum_loans(),
              "H54" => filter_loans_by_product(quarterly_data, "Loans and Leases secured by vehical") |> count_loans(),

              "G59" => filter_loans_by_product(quarterly_data, "Loans and Leases Secured by any other asset") |> sum_loans(),
              "H59" => filter_loans_by_product(quarterly_data, "Loans and Leases Secured by any other asset") |> count_loans(),

              "G60" => filter_loans_by_product(quarterly_data, "Loans and Leases secured by vehical") ++ filter_loans_by_product(quarterly_data, "Loans and Leases Secured by any other asset") |> sum_loans(),
              "H60" => filter_loans_by_product(quarterly_data, "Loans and Leases secured by vehical") ++ filter_loans_by_product(quarterly_data, "Loans and Leases Secured by any other asset") |> count_loans(),

              "G64" => filter_loans_by_product(quarterly_data, "Credit Cards and overdrafts")  |> sum_loans(),
              "H64" => filter_loans_by_product(quarterly_data, "Credit Cards and overdrafts") |> count_loans(),
              "G67" => filter_loans_by_product(quarterly_data, "Credit Cards and overdrafts")  |> sum_loans(),
              "H67" => filter_loans_by_product(quarterly_data, "Credit Cards and overdrafts") |> count_loans(),

              "G77" => filter_loans_by_product(quarterly_data, "Unsecured and Other Loans")  |> sum_loans(),
              "H77" => filter_loans_by_product(quarterly_data, "Unsecured and Other Loans") |> count_loans(),
              "G78" => filter_loans_by_product(quarterly_data, "Unsecured and Other Loans")  |> sum_loans(),
              "H78" => filter_loans_by_product(quarterly_data, "Unsecured and Other Loans") |> count_loans(),

              "G79" => house_hold_zmw_value,
              "H79" => count,

              "G85" => house_hold_zmw_value,
              "H85" => count,
              "G87" => house_hold_zmw_value,
              "H87" => count,


              #=============================2.3.1 Mortgages and other credit agreements
              "C94" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("0"), Decimal.new("50000")) |> sum_loans(),
              "E94" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("0"), Decimal.new("50000")) |> count_loans(),
              "I94" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  0..5) |> sum_loans(),
              "J94" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  0..5) |> count_loans(),

              "C95" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("50001"), Decimal.new("100000")) |> sum_loans(),
              "E95" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("50001"), Decimal.new("100000")) |> count_loans(),
              "I95" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  5..10) |> sum_loans(),
              "J95" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  5..10) |> count_loans(),

              "C96" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("100001"), Decimal.new("500000")) |> sum_loans(),
              "E96" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("100001"), Decimal.new("500000")) |> count_loans(),
              "I96" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  11..15) |> sum_loans(),
              "J96" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  11..15) |> count_loans(),

              "C97" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("500001"), Decimal.new("1000000")) |> sum_loans(),
              "E97" => filter_loans_by_balance_range_and_product(quarterly_data, "Mortgage credit agreement", Decimal.new("500001"), Decimal.new("1000000")) |> count_loans(),
              "I97" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  16..20) |> sum_loans(),
              "J97" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> difference_in_years(end_date,  16..20) |> count_loans(),

              "C98" => filter_loans_by_balance_and_product_range_greatest(quarterly_data, "Mortgage credit agreement", Decimal.new("1000000")) |> sum_loans(),
              "E98" => filter_loans_by_balance_and_product_range_greatest(quarterly_data, "Mortgage credit agreement", Decimal.new("1000000")) |> count_loans(),
              "I98" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> greater_than_in_years(end_date,  20) |> sum_loans(),
              "J98" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> greater_than_in_years(end_date,  20) |> count_loans(),

              "C99" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> sum_loans(),
              "E99" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> count_loans(),
              "I99" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> sum_loans(),
              "J99" => filter_loans_by_product_type_2(quarterly_data, "Mortgage credit agreement") |> count_loans(),
              #========================2.3.2 Leases and asset-backed/secured loans

              "C105" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("0"), Decimal.new("2000")) |> sum_loans(),
              "E105" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("0"), Decimal.new("2000")) |> count_loans(),
              "I105" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  0..3)  |> sum_loans(),
              "J105" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  0..3) |> count_loans(),

              "C106" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("2001"), Decimal.new("5000")) |> sum_loans(),
              "E106" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("2001"), Decimal.new("5000")) |> count_loans(),
              "I106" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  4..6) |> sum_loans(),
              "J106" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  4..6) |> count_loans(),


              "C107" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("5001"), Decimal.new("10000")) |> sum_loans(),
              "E107" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("5001"), Decimal.new("10000")) |> count_loans(),
              "I107" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  7..12) |> sum_loans(),
              "J107" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  7..12) |> count_loans(),


              "C108" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("10001"), Decimal.new("50000")) |> sum_loans(),
              "E108" => filter_loans_by_balance_range_and_product(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("10001"), Decimal.new("50000")) |> count_loans(),
              "I108" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  13..48) |> sum_loans(),
              "J108" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> difference_in_months(end_date,  13..48) |> count_loans(),


              "C109" => filter_loans_by_balance_and_product_range_greatest(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("50001")) |> sum_loans(),
              "E109" => filter_loans_by_balance_and_product_range_greatest(quarterly_data, "Leases and other asset-backed/secured loans", Decimal.new("50001")) |> count_loans(),
              "I109" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> greater_than_in_months(end_date,  48) |> sum_loans(),
              "J109" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> greater_than_in_months(end_date,  48) |> count_loans(),

              "C110" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> sum_loans(),
              "E110" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> count_loans(),
              "I110" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> sum_loans(),
              "J110" => filter_loans_by_product_type_2(quarterly_data, "Leases and other asset-backed/secured loans") |> count_loans(),

              #=========================2.3.3 Unsecured and other loans

              "C116" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("0"), Decimal.new("2000")) |> sum_loans(),
              "E116" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("0"), Decimal.new("2000")) |> count_loans(),
              "I116" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  0..3) |> sum_loans(),
              "J116" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  0..3) |> count_loans(),

              "C117" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("2001"), Decimal.new("5000")) |> sum_loans(),
              "E117" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("2001"), Decimal.new("5000")) |> count_loans(),
              "I117" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  4..6) |> sum_loans(),
              "J117" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  4..6) |> count_loans(),

              "C118" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("5001"), Decimal.new("10000")) |> sum_loans(),
              "E118" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("5001"), Decimal.new("10000")) |> count_loans(),
              "I118" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  7..12) |> sum_loans(),
              "J118" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  7..12) |> count_loans(),

              "C119" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("10001"), Decimal.new("50000")) |> sum_loans(),
              "E119" => filter_loans_by_balance_range_and_product(quarterly_data, "Unsecured and Other Loans", Decimal.new("10001"), Decimal.new("50000")) |> count_loans(),
              "I119" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  13..48) |> sum_loans(),
              "J119" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> difference_in_months(end_date,  13..48) |> count_loans(),

              "C120" => filter_loans_by_balance_and_product_range_greatest(quarterly_data, "Unsecured and Other Loans", Decimal.new("50001")) |> sum_loans(),
              "E120" => filter_loans_by_balance_and_product_range_greatest(quarterly_data, "Unsecured and Other Loans", Decimal.new("50001")) |> count_loans(),
              "I120" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> greater_than_in_months(end_date, 48) |> sum_loans(),
              "J120" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> greater_than_in_months(end_date, 48) |> count_loans(),

              "C121" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> sum_loans(),
              "E121" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> count_loans(),
              "I121" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> sum_loans(),
              "J121" => filter_loans_by_product_type_2(quarterly_data, "Unsecured and Other Loans") |> count_loans(),

              #===============================Section 3.1 Debtors Book Analysis

              "D130" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> sum_loans(),
              "F130" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> count_loans(),
              "G130" => sum_keys(debtors_book_analysis, "allowance_for_losses_mortgages"),
              "H130" => sum_keys(debtors_book_analysis, "number_of_accounts_mortgages"),

              "D131" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> sum_loans(),
              "F131" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> count_loans(),
              "G131" => sum_keys(debtors_book_analysis, "allowance_for_losses_leases"),
              "H131" => sum_keys(debtors_book_analysis, "number_of_accounts_leases"),

              "D132" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> sum_loans(),
              "F132" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> count_loans(),
              "G132" => sum_keys(debtors_book_analysis, "allowance_for_losses_unsecured"),
              "H132" => sum_keys(debtors_book_analysis, "number_of_accounts_unsecured"),

              "D133" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> sum_loans(),
              "F133" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> count_loans(),
              "G133" => sum_keys(debtors_book_analysis, "allowance_for_losses_revolving"),
              "H133" => sum_keys(debtors_book_analysis, "number_of_accounts_revolving"),

              "D134" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans(),
              "F134" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans(),
              "G134" => calculate_allowance_sum(debtors_book_analysis),
              "H134" => number_of_accounts_sum(debtors_book_analysis),
              #=============================Section 3.2: Age Analysis - Current and Overdue accounts

              "D139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("Current") |> sum_loans(),
              "F139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> sum_loans(),
              "G139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> sum_loans(),
              "H139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> sum_loans(),
              "I139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("90-119") |> sum_loans(),
              "J139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("120-179") |> sum_loans(),
              "K139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> sum_loans(),
              "L139" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> sum_loans(),

              "D140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("Current") |> sum_loans(),
              "F140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> sum_loans(),
              "G140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> sum_loans(),
              "H140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |>  sum_loans(),
              "I140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("90-119") |> sum_loans(),
              "J140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("120-179") |> sum_loans(),
              "K140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> sum_loans(),
              "L140" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> sum_loans(),

              "D141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("Current") |> sum_loans(),
              "F141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> sum_loans(),
              "G141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> sum_loans(),
              "H141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> sum_loans(),
              "I141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("90-119") |> sum_loans(),
              "J141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("120-179") |> sum_loans(),
              "K141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> sum_loans(),
              "L141" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> sum_loans(),

              "D142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("Current") |> sum_loans(),
              "F142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> sum_loans(),
              "G142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> sum_loans(),
              "H142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> sum_loans(),
              "I142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("90-119") |> sum_loans(),
              "J142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("120-179") |> sum_loans(),
              "K142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> sum_loans(),
              "L142" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> sum_loans(),

              "D143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("Current") |> sum_loans(),
              "F143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> sum_loans(),
              "G143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> sum_loans(),
              "H143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> sum_loans(),
              "I143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("90-119") |> sum_loans(),
              "J143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("120-179") |> sum_loans(),
              "K143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> sum_loans(),
              "L143" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans(),

              "D144" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("Current") |> sum_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),

              "F144" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> sum_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),

              "G144" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> sum_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),

              "H144" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> sum_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),

              "I144" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("90-119") |> sum_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),

              "J144" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("120-179") |> sum_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),

              "K144" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> sum_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),

              "L144" => percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans(), filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> sum_loans()),
              #=============================3.2.2 Age analysis of performing and non-performing loans: Number of accounts

              "D149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("Current") |> count_loans(),
              "F149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> count_loans(),
              "G149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> count_loans(),
              "H149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> count_loans(),
              "I149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("90-119") |> count_loans(),
              "J149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("120-179") |> count_loans(),
              "K149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> count_loans(),
              "L149" => filter_loans_by_debtors_book_analysis(data, "Mortgage credit agreement") |> count_loans(),

              "D150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("Current") |> count_loans(),
              "F150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> count_loans(),
              "G150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> count_loans(),
              "H150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |>  count_loans(),
              "I150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("90-119") |> count_loans(),
              "J150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("120-179") |> count_loans(),
              "K150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> count_loans(),
              "L150" => filter_loans_by_debtors_book_analysis(data, "Leases and other asset-backed/secured loans") |> count_loans(),

              "D151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("Current") |> count_loans(),
              "F151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> count_loans(),
              "G151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> count_loans(),
              "H151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> count_loans(),
              "I151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("90-119") |> count_loans(),
              "J151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("120-179") |> count_loans(),
              "K151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> count_loans(),
              "L151" => filter_loans_by_debtors_book_analysis(data, "Unsecured and Other Loans") |> count_loans(),

              "D152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("Current") |> count_loans(),
              "F152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> count_loans(),
              "G152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> count_loans(),
              "H152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> count_loans(),
              "I152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("90-119") |> count_loans(),
              "J152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("120-179") |> count_loans(),
              "K152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> count_loans(),
              "L152" => filter_loans_by_debtors_book_analysis(data, "Revolving credit") |> count_loans(),

              "D153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("Current") |> count_loans(),
              "F153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> count_loans(),
              "G153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> count_loans(),
              "H153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> count_loans(),
              "I153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("90-119") |> count_loans(),
              "J153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("120-179") |> count_loans(),
              "K153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> count_loans(),
              "L153" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans(),

              "D154" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("Current") |> count_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),

              "F154" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("1-29 days late") |> count_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),

              "G154" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("30- 59 days") |> count_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),

              "H154" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("60-89 days") |> count_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),

              "I154" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("90-119") |> count_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),

              "J154" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("120-179") |> count_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),

              "K154" => filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data)
                        |> filter_loans_by_age_analysis_days_past_due("180 days and over") |> count_loans()
                        |> percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),

              "L154" => percentage_of_total(filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans(), filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) |> count_loans()),


              }

    format_map(schedule)
  end

  defp filter_loans_by_geo_dist(data, geo_dist) do
    Enum.filter(data, fn loan -> loan["geo_dist"] == geo_dist end)
  end

  defp get_loans_by_province(data, province) do
    Enum.filter(data, fn loan -> loan["province"] == province end)
  end

  defp filter_loans_by_product(data, product_type) do
    Enum.filter(data, fn loan -> loan["type_1"] == product_type end)
  end

  defp filter_loans_by_product_type_2(data, product_type) do
    Enum.filter(data, fn loan -> loan["type_2"] == product_type end)
  end

  defp filter_loans_by_debtors_book_analysis(data, debtors_book_analysis) do
    Enum.filter(data, fn loan -> loan["debtors_book_analysis"] == debtors_book_analysis end)
  end

  defp get_loans_by_gender(data) do
    Enum.filter(data, fn loan -> loan["gender"] == "F" end)
  end

  defp get_loans_by_age(data) do
    Enum.filter(data, fn loan ->
      loan["age"] >= 18 and loan["age"] <= 35
    end)
  end

  defp sum_loans(data) do
    Enum.reduce(data, Decimal.new("0"), fn loan, acc -> Decimal.add(acc, Map.get(loan, "actual_debit_balance", Decimal.new("0"))) end)
  end

  defp sum_keys(data, key) do
    Enum.reduce(data, Decimal.new("0"), fn loan, acc -> Decimal.add(acc, Map.get(loan, key, Decimal.new("0"))) end)
  end

  defp count_loans(data) do
    Enum.count(data)
  end

  defp sum_loans_by_province(data, province) do
    get_loans_by_province(data, province) |> sum_loans()
  end

  defp count_loans_by_province(data, province) do
    get_loans_by_province(data, province) |> count_loans()
  end

  defp percentage_of_total(part, total) do
    total = if Decimal.compare(total, Decimal.new(0)) == :eq, do: Decimal.new(1), else: total
    Decimal.div(part, total || Decimal.new(1)) |> Decimal.mult(Decimal.new("100"))
  end

  def filter_loans_by_balance_range_and_product(data, product_type, min_balance, max_balance) do
    Enum.filter(data, fn data ->
      balance = Map.get(data, "actual_debit_balance", Decimal.new("0"))
      Decimal.cmp(balance, Decimal.new(min_balance)) != :lt and Decimal.cmp(balance, Decimal.new(max_balance)) != :gt and data["type_2"] == product_type
    end)
  end

  def filter_loans_by_balance_and_product_range_greatest(data, product_type, amount) do
    Enum.filter(data, fn loan ->
      balance = Map.get(loan, "actual_debit_balance", Decimal.new("0"))
       Decimal.cmp(balance, amount) == :gt and loan["type_2"] == product_type
    end)
  end

  def filter_loans_by_debtors_book_analysis_and_days_past_due(data, debtors_book_analysis) do
    Enum.filter(data, fn loan -> loan["debtors_book_analysis"] == debtors_book_analysis end)
  end

  def filter_loans_by_debtors_book_analysis_and_days_past_due_all_products(data) do
    Enum.filter(data, fn loan ->
      loan["debtors_book_analysis"] in ["Revolving credit", "Unsecured and Other Loans", "Leases and other asset-backed/secured loans", "Mortgage credit agreement"]
    end)
  end

  def days_to_months(days) do
    months = days / 30.0  # Average days in a month
    Math.round(months, 1)
  end

  def difference_in_years(data, date, range) do
    Enum.filter(data, fn loan ->
      Timex.diff(loan["account_maturity_date"] || date, date, :years) in Enum.to_list(range)
    end)
  end

  def difference_in_months(data, date, range) do
    Enum.filter(data, fn data ->
      Timex.diff(data["account_maturity_date"] || date, date, :months) in  Enum.to_list(range)
    end)
  end

  def difference_in_days(data, date, range) do
    Enum.filter(data, fn loan ->
      Timex.diff(loan["account_maturity_date"] || date, date, :days) in  Enum.to_list(range)
    end)
  end

  def greater_than_in_years(data, date, range) do
    Enum.filter(data, fn loan ->
      Timex.diff(loan["account_maturity_date"] || date, date, :years) > range
    end)
  end

  def greater_than_in_months(data, date, range) do
    Enum.filter(data, fn loan ->
      Timex.diff(loan["account_maturity_date"] || date, date, :months) > range
    end)
  end

  def greater_than_in_days(data, date, range) do
    Enum.filter(data, fn loan ->
      Timex.diff(loan["account_maturity_date"] || date, date, :days) > range
    end)
  end

  def loan_book_age_analysis_classification(data) do
    Enum.map(data, fn loan ->
      Map.merge(loan, format_days_past_due(loan["loan_classification"], loan["days_past_due"]))
    end)
  end

  def format_days_past_due(loan_classification, days_past_due) do
    case loan_classification do
      "PL" ->

        cond do
          days_past_due in [nil, 0] ->
            %{"age_analysis" => "Current"}

          days_past_due in Enum.to_list(1..29) ->
            %{"age_analysis" => "1-29 days late"}

          days_past_due in Enum.to_list(30..59) ->
            %{"age_analysis" => "30- 59 days"}

          days_past_due in Enum.to_list(60..89) ->
            %{"age_analysis" => "60-89 days"}

          days_past_due in Enum.to_list(90..119) ->
            %{"age_analysis" => "90-119"}

          days_past_due in Enum.to_list(120..179) ->
            %{"age_analysis" => "120-179"}

          days_past_due > 180 ->
            %{"age_analysis" => "180 days and over"}

        true ->
          %{"age_analysis" => "Current"}
        end

      "NPL" ->

        cond do
          days_past_due in [nil, 0] ->
            %{"age_analysis" => "90-119"}

          days_past_due in Enum.to_list(1..29) ->
            %{"age_analysis" => "90-119"}

          days_past_due in Enum.to_list(30..59) ->
            %{"age_analysis" => "90-119"}

          days_past_due in Enum.to_list(60..89) ->
            %{"age_analysis" => "90-119"}

          days_past_due in Enum.to_list(90..119) ->
            %{"age_analysis" => "90-119"}

          days_past_due in Enum.to_list(120..179) ->
            %{"age_analysis" => "120-179"}

          days_past_due > 180 ->
            %{"age_analysis" => "180 days and over"}

        true ->
          %{"age_analysis" => "90-119"}
        end
    end

 end

  def filter_loans_by_age_analysis_days_past_due(data, age_analysis) do
    Enum.filter(data, fn loan ->  loan["age_analysis"] == age_analysis  end)
  end

  defp calculate_allowance_sum(data) do

    keys_to_sum = [
      "allowance_for_losses_unsecured",
      "allowance_for_losses_revolving",
      "allowance_for_losses_mortgages",
      "allowance_for_losses_leases"
    ]

    Enum.reduce(keys_to_sum, Decimal.new("0"), fn key, acc ->
      Enum.reduce(data, acc, fn item, acc_inner ->
        Decimal.add(acc_inner, Map.get(item, key, Decimal.new("0")))
      end)
    end)
  end

  defp number_of_accounts_sum(data) do

    keys_to_sum = [
      "number_of_accounts_mortgages",
      "number_of_accounts_leases",
      "number_of_accounts_unsecured",
      "number_of_accounts_revolving"
    ]

    Enum.reduce(keys_to_sum, Decimal.new("0"), fn key, acc ->
      Enum.reduce(data, acc, fn item, acc_inner ->
        Decimal.add(acc_inner, Map.get(item, key, Decimal.new("0")))
      end)
    end)
  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end

  defp format_number(val) when is_binary(val), do: val

  defp format_number(val) do
    case is_binary(val) do
      true -> val
      false ->
        if val == nil do
          ""
        else
          v = to_string(val)|> Decimal.new()
          float = Decimal.to_float(v)
          Number.Delimit.number_to_delimited(float, [
            precision: 2,
            delimiter: ",",
            separator: "."
          ])
        end

    end
  end
end
