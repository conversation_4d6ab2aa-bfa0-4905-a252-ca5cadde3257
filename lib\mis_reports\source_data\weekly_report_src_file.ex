defmodule MisReports.SourceData.WeeklyReportSrcFile do
  use Ecto.Schema
  import Ecto.Changeset

  schema "weekly_report_src_files" do
    field :date, :date
    field :filename, :string
    field :month, :string
    field :status, :string, default: "PENDING_UPLOAD"
    field :year, :string
    field :reference, :string, default: Ecto.UUID.generate()
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(weekly_report_src_file, attrs) do
    weekly_report_src_file
    |> cast(attrs, [:filename, :status, :date, :month, :year, :maker_id, :checker_id, :reference])
    |> validate_required([:filename, :status, :date, :month, :year])
  end
end
