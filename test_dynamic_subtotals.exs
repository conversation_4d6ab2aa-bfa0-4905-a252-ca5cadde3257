# Test script for dynamic subtotals in IFRS Trend
# Run with: elixir test_dynamic_subtotals.exs

# Mock data for testing
defmodule MockData do
  def sample_data do
    [
      %{gl_no: "100001", january: Decimal.new(1000), february: Decimal.new(1100), march: Decimal.new(1200)},
      %{gl_no: "100002", january: Decimal.new(2000), february: Decimal.new(2100), march: Decimal.new(2200)},
      %{gl_no: "200001", january: Decimal.new(500), february: Decimal.new(550), march: Decimal.new(600)},
      %{gl_no: "200002", january: Decimal.new(300), february: Decimal.new(330), march: Decimal.new(360)},
    ]
  end
end

# Mock Mappings module
defmodule MockMappings do
  def get_gl("BASIC_PAY"), do: ["100001"]
  def get_gl("ALLOWANCE"), do: ["100002"] 
  def get_gl("OCCUPANCY"), do: ["200001"]
  def get_gl("EQUIPMENT"), do: ["200002"]
  def get_gl(_), do: []
end

# Test the dynamic subtotal functionality
defmodule TestDynamicSubtotals do
  def run_test do
    IO.puts("Testing Dynamic Subtotals...")
    
    # Test data
    data = MockData.sample_data()
    
    # Test mapping with dynamic subtotals
    test_mapping = [
      # Dynamic subtotal at the top - sums keys from anywhere
      {:subtotal, "Total Staff Costs", keys: ["BASIC_PAY", "ALLOWANCE"]},
      
      # Regular groups
      {"BASIC_PAY", "Basic Pay"},
      {"ALLOWANCE", "Allowances"},
      
      # Another dynamic subtotal
      {:subtotal, "Total Operating Costs", keys: ["OCCUPANCY", "EQUIPMENT"]},
      
      {"OCCUPANCY", "Premises Expenses"},
      {"EQUIPMENT", "Equipment Expenses"},
      
      # Complex subtotal combining previous subtotals
      {:subtotal, "Grand Total", include: ["Total Staff Costs", "Total Operating Costs"]}
    ]
    
    IO.puts("Test mapping created successfully!")
    IO.puts("Dynamic subtotals can now reference keys from any position!")
    
    # Show the power of the new system
    IO.puts("\n=== Key Benefits ===")
    IO.puts("1. Position Independence: Subtotals can be placed anywhere")
    IO.puts("2. Flexible Key Selection: Reference any keys by name")
    IO.puts("3. Exclusion Support: Exclude specific keys from calculations")
    IO.puts("4. Subtotal Composition: Combine multiple subtotals")
    IO.puts("5. Backward Compatibility: Old subtotal syntax still works")
    
    :ok
  end
end

# Run the test
TestDynamicSubtotals.run_test()
