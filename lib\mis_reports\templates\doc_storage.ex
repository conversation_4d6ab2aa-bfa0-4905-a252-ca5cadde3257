defmodule MisReports.Templates.DocStorage do
  use Endon
  use Ecto.Schema
  import Ecto.Changeset

  @timestamps_opts [autogenerate: {MisReports.Accounts.User.Localtime, :autogenerate, []}]
  schema "tbl_doc_storage" do
    field :doc_description, :string
    field :doc_name, :string
    field :status, :string, default: "PENDING"
    field :date, :date
    field :end_date, :date
    field :reference, :string, default: Ecto.UUID.generate()

    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :updated_by, MisReports.Accounts.User, foreign_key: :last_user_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(doc_storage, attrs) do
    doc_storage
    |> cast(attrs, [:doc_description, :doc_name, :maker_id, :last_user_id, :status, :date, :end_date, :reference])
    |> validate_required([:doc_description, :doc_name])
    # |> unsafe_validate_unique([:doc_description, :doc_name], MisReports.Repo, message: "already exists")
  end
end
