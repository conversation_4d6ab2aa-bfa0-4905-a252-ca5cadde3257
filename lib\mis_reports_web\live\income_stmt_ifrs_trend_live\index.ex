defmodule MisReportsWeb.IncomeStmtIfrsTrendLive.Index do
  use MisReportsWeb, :live_view
  on_mount MisReportsWeb.UserLiveAuth

  alias MisReports.Workers.ReportGens.Mis.IfrsTrend

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       data: [],
       start_date: nil,
       end_date: nil,
       loading: false,
       report_type: ""
     )}
  end

  @impl true
  def handle_event("filter-report", %{"report" => params}, socket) do
    socket = assign(socket, loading: true)
    Process.send_after(self(), {:load_data, params}, 100)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:load_data, params}, socket) do
    end_date = params["end_date"]

    trend_data = IfrsTrend.generate_display(end_date)

    # Parse and format dates
    {:ok, end_date_dt} = Date.from_iso8601(end_date)
    start_date_dt = %Date{year: end_date_dt.year, month: 1, day: 1}

    human_start = Calendar.strftime(start_date_dt, "%-d %B %Y")
    human_end = Calendar.strftime(end_date_dt, "%-d %B %Y")

    socket =
      socket
      |> assign(data: trend_data)
      |> assign(start_date: start_date_dt)
      |> assign(end_date: end_date_dt)
      |> assign(human_start: human_start)
      |> assign(human_end: human_end)
      |> assign(report_type: "trend_report")
      |> assign(loading: false)

    {:noreply, socket}
  end
end
