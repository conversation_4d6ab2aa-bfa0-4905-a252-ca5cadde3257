defmodule MisReports.Workers.ReportGens.Mis.IfrsTrend do
  @moduledoc """
  Module for generating IFRS trend reports
  """

  alias MisReports.SourceData
  alias MisReports.Mappings

  @mapping_groups [
    {"LOAN_ADVANCES_NORMAL_DEPOSITS", "Interest income from Loans and advances"},
    {"FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS", "Interest income from loans to banks"},
    {"M305053_Interest_Income_treasury_bills", "M305053 Interest Income from treasury bills"},
    {"M305054_INTEREST_INCOME_GOVT_BONDS", "M305054 Interest income from Govt Bonds"},
    {"LEASING_INCOME_FROM_NORMAL_DEPOSITS", "M305069 Leasing income from normal deposits"},
    {"CREDIT_CARDS", "Credit Cards"},
    {"ALL_OTHER", "All Other"},
    {:subtotal, "Interest Income for Regulatory reporting"}, # <-- subtotal marker
    #total regulatory value for the above keys
    #total deposits
    {"DEPOSITS_FOREIGN_LOCAL_CURRENCY_DEMAND", "M311175 Demand"},
    {"TIME", "Time"},
    {"DEPOSITS_FOREIGN_LOCAL_CURRENCY_SAVINGS", "M311171 Savings"},
    {:subtotal, "Deposits"},
    {"INTEREST_TO_BANKS", "Interest paid to banks and financial institutions"},
    {"SUBORDINATED_DEBT", "Subordinated Debt"},
    {"ALL_OTHER_INTEREST_EXPENSES", "All Other"},
    {:subtotal, "Interest Expense for Regulatory Reporting", include: ["Deposits"]},
    {:subtotal, "Net Interest Income", include: ["Interest Income for Regulatory reporting", "Interest Expense for Regulatory Reporting"]},
    #Interest Expense for Regulatory Reporting
    {"GENERAL_DEBT_PROVISIONING_PRUDENTIAL", "General Debt Provisioning in Prudential"},
    {"M313534_STAGE_3_SDP_IMPAIRMENT_CHARGES", "M313534  Stage 3 SDP Impairment charges"},
    {"TOTAL_OTHER_PROVISIONS_CHARGE_IN_PRUDENTIAL", "Total Other Provisions Charge in Prudential"},
    {:subtotal, "Credit impairment charge - Reg"},
    #total Credit impairment charge - Reg
    {"COMMISION_FEES", "Commission Fees"},
    {"FEES_FROM_FOREIGN_EXCHANGE", "Fees from Foreign Exchange"},
    {:subtotal, "Total M317000 Total Fee & Commission"},
    #total M317000  Total Fee & Commission
    {"TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES", "Trading income to be report in prudential"},
    {:subtotal, "Total Trading income to be report in prudential"},
    {"ID_TRADING_INCOME", "ID Trading Income"},
    {:subtotal, "Total ID Trading Income"},
    {"ID_INTEREST_RECEIVED_TRANSFERED_TO_TRADING_INCOME", "ID Interest Received Transfered to Trading Income"},
    {:subtotal, "Total ID Interest Received Transfered to Trading Income"},
    {"ID_INTEREST_EXPENSE_TRANSFERED_TO_TRADING_INCOME", "ID Interest Expense Transfered to Trading Income"},
    {"FX_GAIN_LOSS", "FX Gain/Loss"},
    {"SUNDRY_INCOME", "Sundry Income"},
    {"SUNDRY_INCOME_OTHER", "Sundry Income"},
    {"RENTAL_INCOME", "Rental Income"},
    {"FX_GAIN_LOSS_2", "FX Gain/Loss"},
    {"INCOME_FROM_DISPOSAL_OF_ASSETS", "Income from Disposal of Assets"},
    {"TOTAL_WRITE_BACKS_TRANSFERRED", "Total Write Backs Transferred to Income"},
    {"BASIC_PAY", "Basic Pay"},
    {"ALLOWANCE", "Allowances"},
    {"BONUSES", "Bonuses"},
    {"PENSION", "Pension"},
    {"NAPSA", "NAPSA"},
    {"OVERTIME_PAY", "Overtime Pay"},
    {"OTHER_BENEFITS", "Other Benefits"},
    {"OCCUPANCY", "M328000  Premises Expenses"},
    {"EQUIPMENT", "Equipment"},
    {"DEPRECIATION", "M329600  Depreciation Expenses"},
    {"EDUCATION_AND_TRAINING", "M332420 Training Expenses"},
    {"AUDIT_LEGAL_AND_PROFESSIONAL_FEES", "Audit, Legal and Professional Fees"},
    {"INSURANCE", "M340621 Insurance"},
    {"MANAGEMENT_FEES", "M332050 IC:Prof Fees - Technical & Other"},
    {"DONATIONS", "Donations"},
    {"M332220_ADMINISTRATION_AND_MEMBERSHIP_FEES", "M332220 Administration and Membership Fees"},
    {"M332620_COMMISSION_PAID", "M332620 Commission Paid"},
    {"M332720_SECURITY_EXPENSES", "M332720 Security Expenses"},
    {"M329700_AMORTIZATION_OF_INTANGIBLES", "M329700 Amortization of Intangibles"},
    {"M340070_SOFTWARE_MAINTENANCE", "M340070 Software Maintenance"},
    {"M340120_COMMUNICATION_EXPENSES", "M340120 Communication Expenses"},
    {"M340200_TRAVEL_ENTERTAINMENT", "M340200 Travel and Entertainment"},
    {"M340320_PROCESSING_COSTS", "M340320 Processing Costs"},
    {"M340420_STATIONARY_PRINTING", "M340420 Stationary Printing"},
    {"M340520_MARKETING_AND_ADVERTISING", "M340520 Marketing and Advertising"},
    {"M340600_OPERATIONAL_RISK_LOSSES", "M340600 Operational Risk Losses"},
    {"M300701_MISCELLANEOUS_COSTS", "M300701 Miscellaneous Costs"},
    {"M300716_BANK_CHARGES_PENALTIES_AND_FINES", "M300716 Bank charges penalties and fines"},
    {"M300705_MOTORING_EXPENSES", "M300705 Motoring Expenses"},
    {"M300707_DIRECTORS", "M300707 Directors"},
    {"M300713_FX_AND_COST_HEDGE", "M300713 FX and Cost Hedge"},
    {"M340800_IC_OPERATING_EXPENSES", "M340800 IC Operating Expenses"},
    {"M353000_INDIRECT_TAXES", "M353000 Indirect Taxes"},
    {"TAX", "Tax"}
  ]

  #multiply by minus 1
  @negative_keys [
    "LOAN_ADVANCES_NORMAL_DEPOSITS",
    "FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS",
    "M305053_Interest_Income_treasury_bills",
    "M305054_INTEREST_INCOME_GOVT_BONDS",
    "LEASING_INCOME_FROM_NORMAL_DEPOSITS",
    "CREDIT_CARDS",
    "ALL_OTHER",
    "DEPOSITS_FOREIGN_LOCAL_CURRENCY_DEMAND",
    "TIME",
    "DEPOSITS_FOREIGN_LOCAL_CURRENCY_SAVINGS",
    "INTEREST_TO_BANKS",
    "SUBORDINATED_DEBT",
    "ALL_OTHER_INTEREST_EXPENSES",
    "GENERAL_DEBT_PROVISIONING_PRUDENTIAL",
    "M313534_STAGE_3_SDP_IMPAIRMENT_CHARGES",
    "TOTAL_OTHER_PROVISIONS_CHARGE_IN_PRUDENTIAL",
    "COMMISION_FEES",
    "FEES_FROM_FOREIGN_EXCHANGE",
    "TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES",
    "ID_INTEREST_RECEIVED_TRANSFERED_TO_TRADING_INCOME",
    "FX_GAIN_LOSS",
    "SUNDRY_INCOME",
    "SUNDRY_INCOME_OTHER",
    "FX_GAIN_LOSS_2",
    "INCOME_FROM_DISPOSAL_OF_ASSETS",
    "TOTAL_WRITE_BACKS_TRANSFERRED",
    "ALLOWANCE",
    "BONUSES",
    "PENSION",
    "NAPSA",
    "OVERTIME_PAY",
    "OTHER_BENEFITS",
    "OCCUPANCY",
    "EQUIPMENT",
    "DEPRECIATION",
    "EDUCATION_AND_TRAINING",
    "AUDIT_LEGAL_AND_PROFESSIONAL_FEES",
    "INSURANCE",
    "MANAGEMENT_FEES",
    "DONATIONS",
    "M332220_ADMINISTRATION_AND_MEMBERSHIP_FEES",
    "M332620_COMMISSION_PAID",
    "M329700_AMORTIZATION_OF_INTANGIBLES",
    "M340070_SOFTWARE_MAINTENANCE",
    "M340120_COMMUNICATION_EXPENSES",
    "M340320_PROCESSING_COSTS",
    "M340420_STATIONARY_PRINTING",
    "M340520_MARKETING_AND_ADVERTISING",
    "M340600_OPERATIONAL_RISK_LOSSES",
    "M300701_MISCELLANEOUS_COSTS",
    "M300716_BANK_CHARGES_PENALTIES_AND_FINES",
    "M300705_MOTORING_EXPENSES",
    "M300707_DIRECTORS",
    "M300713_FX_AND_COST_HEDGE",
    "M340800_IC_OPERATING_EXPENSES",
    "M353000_INDIRECT_TAXES",
    "TAX"
  ]

  @value_keys [
    :january, :february, :march, :q1,
    :april, :may, :june, :q2,
    :july, :august, :september, :q3,
    :october, :november, :december, :q4,
    :ytd
  ]

  @doc """
  Generates the IFRS trend report data for the specified date.
  """
  # MisReports.Workers.ReportGens.Mis.IfrsTrend.generate_display("2025-03-31")
  def generate_display(to) do
    data = SourceData.get_ifrs_trend_data(to)
    format_data(data)
  end

  def format_data(data) do
    {result, acc_groups, all_sections} =
      Enum.reduce(@mapping_groups, {[], [], %{}}, fn
        {:subtotal, label, opts}, {result, acc_groups, all_sections} ->
          # Gather groups for this subtotal
          section_groups = acc_groups

          # Gather additional groups/subtotals to include, if any
          include_labels = Keyword.get(opts, :include, [])
          included_groups =
            include_labels
            |> Enum.flat_map(fn inc_label ->
              Map.get(all_sections, inc_label, [])
            end)

          subtotal_groups = section_groups ++ included_groups
          subtotal = compute_section_total(subtotal_groups, label)

          # Store this subtotal's groups for future composite subtotals
          {
            result ++ section_groups ++ [subtotal],
            [],
            Map.put(all_sections, label, subtotal_groups)
          }

        {:subtotal, label}, {result, acc_groups, all_sections} ->
          subtotal = compute_section_total(acc_groups, label)
          {
            result ++ acc_groups ++ [subtotal],
            [],
            Map.put(all_sections, label, acc_groups)
          }

        {key, title}, {result, acc_groups, all_sections} ->
          gls = Mappings.get_gl(key)
          rows = Enum.filter(data, fn row -> row.gl_no in gls end)
          total = sum_group_totals(rows, key)

          processed_rows =
            Enum.map(rows, fn row ->
              Enum.reduce(@value_keys, row, fn k, acc ->
                Map.update(acc, k, nil, fn v ->
                  if key in @negative_keys and is_number_or_decimal(v) do
                    Decimal.mult(v, -1)
                  else
                    v
                  end
                end)
              end)
            end)

          processed_total =
            Enum.reduce(@value_keys, %{}, fn k, acc ->
              v =
                Map.get(total, k, nil)
                |> (fn v ->
                  if key in @negative_keys and is_number_or_decimal(v), do: Decimal.mult(v, -1), else: v
                end).()

              Map.put(acc, k, v)
            end)

          group = %{
            key: key,
            title: title,
            total: processed_total,
            rows: processed_rows
          }

          {result, acc_groups ++ [group], all_sections}
      end)

    # After all, format numbers for display
    Enum.map(result ++ acc_groups, fn group ->
      %{
        group
        | total: Enum.into(group.total, %{}, fn {k, v} -> {k, format_and_wrap_number(v)} end),
          rows: Enum.map(group.rows, fn row ->
            Enum.into(row, %{}, fn
              {k, v} when k in @value_keys -> {k, format_and_wrap_number(v)}
              pair -> pair
            end)
          end)
      }
    end)
  end

  defp compute_section_total(section, label) do
    total =
      Enum.reduce(@value_keys, %{}, fn k, acc ->
        sum =
          section
          |> Enum.map(fn g ->
            v = g.total[k]
            cond do
              v == nil -> Decimal.new(0)
              is_binary(v) ->
                clean = v |> String.replace(",", "") |> String.replace("(", "-") |> String.replace(")", "")
                Decimal.new(clean)
              true -> v
            end
          end)
          |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

        Map.put(acc, k, sum)
      end)

    formatted_total = Enum.into(total, %{}, fn {k, v} -> {k, format_and_wrap_number(v)} end)

    %{
      key: "SUBTOTAL_#{label |> String.replace(" ", "_") |> String.upcase()}",
      title: label,
      total: formatted_total,
      rows: []
    }
  end

  defp sum_group_totals(rows, _group_key) do
    Enum.reduce(@value_keys, %{}, fn key, acc ->
      total =
        rows
        |> Enum.map(&Map.get(&1, key, Decimal.new(0)))
        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

      Map.put(acc, key, total)
    end)
  end

  def format_and_wrap_number(val) do
    cond do
      is_binary(val) or val == nil ->
        val

      true ->
        rebased =
          val
          |> Decimal.div(Decimal.new(1000))
          |> Decimal.round(0)

        float = Decimal.to_float(rebased)
        formatted = Number.Delimit.number_to_delimited(float, [
          precision: 0,
          delimiter: ",",
          separator: "."
        ])

        if Decimal.compare(rebased, 0) == :lt do
          "(" <> String.replace(formatted, "-", "") <> ")"
        else
          formatted
        end
    end
  end

  defp is_number_or_decimal(val) do
    is_number(val) or match?(%Decimal{}, val)
  end

end
