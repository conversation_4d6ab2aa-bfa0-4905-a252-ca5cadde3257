defmodule MisReports.Workers.ReportGens.Mis.IfrsTrend do
  @moduledoc """
  Module for generating IFRS trend reports with flexible subtotal positioning.

  ## Subtotal Options

  The module now supports flexible subtotal positioning with the following options:

  ### Basic Subtotal (Backward Compatible)
  ```elixir
  {:subtotal, "Label"}
  ```
  Sums all groups that appear before this subtotal in the mapping list.

  ### Subtotal with Include References
  ```elixir
  {:subtotal, "Label", include: ["Other Subtotal Name", "GROUP_KEY"]}
  ```
  Includes specific subtotals or group keys in the calculation.

  ### Dynamic Subtotal with Specific Keys (Recommended)
  ```elixir
  {:subtotal, "Label", keys: ["GROUP_KEY_1", "GROUP_KEY_2"]}
  ```
  Sums only the specified group keys, regardless of their position in the mapping list.

  ### Dynamic Subtotal with Key Ranges
  ```elixir
  {:subtotal, "Label", keys: ["KEY_1", "KEY_2"], exclude: ["KEY_TO_SKIP"]}
  ```
  Sums specified keys but excludes certain ones.

  ### Combined Dynamic Options
  ```elixir
  {:subtotal, "Label", include: ["Previous Subtotal"], keys: ["SPECIFIC_KEY"], exclude: ["UNWANTED_KEY"]}
  ```
  Combines subtotal references, specific keys, and exclusions.

  ## Dynamic Examples

  ```elixir
  # Sum specific salary components from anywhere in the list
  {:subtotal, "Total Staff Costs", keys: ["BASIC_PAY", "ALLOWANCE", "BONUSES", "PENSION", "NAPSA", "OVERTIME_PAY", "OTHER_BENEFITS"]}

  # Sum all interest income items regardless of position
  {:subtotal, "Total Interest Income", keys: ["LOAN_ADVANCES_NORMAL_DEPOSITS", "FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS", "M305053_Interest_Income_treasury_bills", "M305054_INTEREST_INCOME_GOVT_BONDS"]}

  # Position-independent subtotal at the top of the report
  {:subtotal, "Executive Summary", keys: ["COMMISION_FEES", "FEES_FROM_FOREIGN_EXCHANGE"], exclude: ["UNWANTED_FEE"]}

  # Complex calculation combining multiple sources
  {:subtotal, "Net Operating Income", include: ["Total Interest Income"], keys: ["TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES"], exclude: ["EXCEPTIONAL_ITEMS"]}
  ```
  """

  alias MisReports.SourceData
  alias MisReports.Mappings

  @mapping_groups [
    # DYNAMIC SUBTOTAL EXAMPLES - These can be positioned anywhere and reference any keys
    {:subtotal, "Total Staff Costs", keys: ["BASIC_PAY", "ALLOWANCE", "BONUSES", "PENSION", "NAPSA", "OVERTIME_PAY", "OTHER_BENEFITS"]},
    {:subtotal, "Total Operating Expenses", keys: ["OCCUPANCY", "EQUIPMENT", "DEPRECIATION", "EDUCATION_AND_TRAINING", "AUDIT_LEGAL_AND_PROFESSIONAL_FEES", "INSURANCE"]},

    # Regular groups
    {"LOAN_ADVANCES_NORMAL_DEPOSITS", "Interest income from Loans and advances"},
    {"FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS", "Interest income from loans to banks"},
    {"M305053_Interest_Income_treasury_bills", "M305053 Interest Income from treasury bills"},
    {"M305054_INTEREST_INCOME_GOVT_BONDS", "M305054 Interest income from Govt Bonds"},
    {"LEASING_INCOME_FROM_NORMAL_DEPOSITS", "M305069 Leasing income from normal deposits"},
    {"CREDIT_CARDS", "Credit Cards"},
    {"ALL_OTHER", "All Other"},

    # Dynamic subtotal for all interest income (regardless of position)
    {:subtotal, "Total Interest Income", keys: ["LOAN_ADVANCES_NORMAL_DEPOSITS", "FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS", "M305053_Interest_Income_treasury_bills", "M305054_INTEREST_INCOME_GOVT_BONDS", "LEASING_INCOME_FROM_NORMAL_DEPOSITS", "CREDIT_CARDS", "ALL_OTHER"]},

    {:subtotal, "Interest Income for Regulatory reporting"}, # <-- backward compatible subtotal
    #total regulatory value for the above keys
    #total deposits
    {"DEPOSITS_FOREIGN_LOCAL_CURRENCY_DEMAND", "M311175 Demand"},
    {"TIME", "Time"},
    {"DEPOSITS_FOREIGN_LOCAL_CURRENCY_SAVINGS", "M311171 Savings"},
    {:subtotal, "Deposits"},
    {"INTEREST_TO_BANKS", "Interest paid to banks and financial institutions"},
    {"SUBORDINATED_DEBT", "Subordinated Debt"},
    {"ALL_OTHER_INTEREST_EXPENSES", "All Other"},
    {:subtotal, "Interest Expense for Regulatory Reporting", include: ["Deposits"]},
    {:subtotal, "Net Interest Income", include: ["Interest Income for Regulatory reporting", "Interest Expense for Regulatory Reporting"]},
    #Interest Expense for Regulatory Reporting
    {"GENERAL_DEBT_PROVISIONING_PRUDENTIAL", "General Debt Provisioning in Prudential"},
    {"M313534_STAGE_3_SDP_IMPAIRMENT_CHARGES", "M313534  Stage 3 SDP Impairment charges"},
    {"TOTAL_OTHER_PROVISIONS_CHARGE_IN_PRUDENTIAL", "Total Other Provisions Charge in Prudential"},
    {:subtotal, "Credit impairment charge - Reg"},
    #total Credit impairment charge - Reg
    {"COMMISION_FEES", "Commission Fees"},
    {"FEES_FROM_FOREIGN_EXCHANGE", "Fees from Foreign Exchange"},
    {:subtotal, "Total M317000 Total Fee & Commission"},
    #total M317000  Total Fee & Commission
    {"TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES", "Trading income to be report in prudential"},
    {:subtotal, "Total Trading income to be report in prudential"},
    {"ID_TRADING_INCOME", "ID Trading Income"},
    {:subtotal, "Total ID Trading Income"},
    {"ID_INTEREST_RECEIVED_TRANSFERED_TO_TRADING_INCOME", "ID Interest Received Transfered to Trading Income"},
    {:subtotal, "Total ID Interest Received Transfered to Trading Income"},
    {"ID_INTEREST_EXPENSE_TRANSFERED_TO_TRADING_INCOME", "ID Interest Expense Transfered to Trading Income"},
    {"FX_GAIN_LOSS", "FX Gain/Loss"},
    {"SUNDRY_INCOME", "Sundry Income"},
    {"SUNDRY_INCOME_OTHER", "Sundry Income"},
    {"RENTAL_INCOME", "Rental Income"},
    {"FX_GAIN_LOSS_2", "FX Gain/Loss"},
    {"INCOME_FROM_DISPOSAL_OF_ASSETS", "Income from Disposal of Assets"},
    {"TOTAL_WRITE_BACKS_TRANSFERRED", "Total Write Backs Transferred to Income"},
    {"BASIC_PAY", "Basic Pay"},
    {"ALLOWANCE", "Allowances"},
    {"BONUSES", "Bonuses"},
    {"PENSION", "Pension"},
    {"NAPSA", "NAPSA"},
    {"OVERTIME_PAY", "Overtime Pay"},
    {"OTHER_BENEFITS", "Other Benefits"},
    {"OCCUPANCY", "M328000  Premises Expenses"},
    {"EQUIPMENT", "Equipment"},
    {"DEPRECIATION", "M329600  Depreciation Expenses"},
    {"EDUCATION_AND_TRAINING", "M332420 Training Expenses"},
    {"AUDIT_LEGAL_AND_PROFESSIONAL_FEES", "Audit, Legal and Professional Fees"},
    {"INSURANCE", "M340621 Insurance"},
    {"MANAGEMENT_FEES", "M332050 IC:Prof Fees - Technical & Other"},
    {"DONATIONS", "Donations"},
    {"M332220_ADMINISTRATION_AND_MEMBERSHIP_FEES", "M332220 Administration and Membership Fees"},
    {"M332620_COMMISSION_PAID", "M332620 Commission Paid"},
    {"M332720_SECURITY_EXPENSES", "M332720 Security Expenses"},
    {"M329700_AMORTIZATION_OF_INTANGIBLES", "M329700 Amortization of Intangibles"},
    {"M340070_SOFTWARE_MAINTENANCE", "M340070 Software Maintenance"},
    {"M340120_COMMUNICATION_EXPENSES", "M340120 Communication Expenses"},
    {"M340200_TRAVEL_ENTERTAINMENT", "M340200 Travel and Entertainment"},
    {"M340320_PROCESSING_COSTS", "M340320 Processing Costs"},
    {"M340420_STATIONARY_PRINTING", "M340420 Stationary Printing"},
    {"M340520_MARKETING_AND_ADVERTISING", "M340520 Marketing and Advertising"},
    {"M340600_OPERATIONAL_RISK_LOSSES", "M340600 Operational Risk Losses"},
    {"M300701_MISCELLANEOUS_COSTS", "M300701 Miscellaneous Costs"},
    {"M300716_BANK_CHARGES_PENALTIES_AND_FINES", "M300716 Bank charges penalties and fines"},
    {"M300705_MOTORING_EXPENSES", "M300705 Motoring Expenses"},
    {"M300707_DIRECTORS", "M300707 Directors"},
    {"M300713_FX_AND_COST_HEDGE", "M300713 FX and Cost Hedge"},
    {"M340800_IC_OPERATING_EXPENSES", "M340800 IC Operating Expenses"},
    {"M353000_INDIRECT_TAXES", "M353000 Indirect Taxes"},
    {"TAX", "Tax"}
  ]

  #multiply by minus 1
  @negative_keys [
    "LOAN_ADVANCES_NORMAL_DEPOSITS",
    "FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS",
    "M305053_Interest_Income_treasury_bills",
    "M305054_INTEREST_INCOME_GOVT_BONDS",
    "LEASING_INCOME_FROM_NORMAL_DEPOSITS",
    "CREDIT_CARDS",
    "ALL_OTHER",
    "DEPOSITS_FOREIGN_LOCAL_CURRENCY_DEMAND",
    "TIME",
    "DEPOSITS_FOREIGN_LOCAL_CURRENCY_SAVINGS",
    "INTEREST_TO_BANKS",
    "SUBORDINATED_DEBT",
    "ALL_OTHER_INTEREST_EXPENSES",
    "GENERAL_DEBT_PROVISIONING_PRUDENTIAL",
    "M313534_STAGE_3_SDP_IMPAIRMENT_CHARGES",
    "TOTAL_OTHER_PROVISIONS_CHARGE_IN_PRUDENTIAL",
    "COMMISION_FEES",
    "FEES_FROM_FOREIGN_EXCHANGE",
    "TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES",
    "ID_INTEREST_RECEIVED_TRANSFERED_TO_TRADING_INCOME",
    "FX_GAIN_LOSS",
    "SUNDRY_INCOME",
    "SUNDRY_INCOME_OTHER",
    "FX_GAIN_LOSS_2",
    "INCOME_FROM_DISPOSAL_OF_ASSETS",
    "TOTAL_WRITE_BACKS_TRANSFERRED",
    "ALLOWANCE",
    "BONUSES",
    "PENSION",
    "NAPSA",
    "OVERTIME_PAY",
    "OTHER_BENEFITS",
    "OCCUPANCY",
    "EQUIPMENT",
    "DEPRECIATION",
    "EDUCATION_AND_TRAINING",
    "AUDIT_LEGAL_AND_PROFESSIONAL_FEES",
    "INSURANCE",
    "MANAGEMENT_FEES",
    "DONATIONS",
    "M332220_ADMINISTRATION_AND_MEMBERSHIP_FEES",
    "M332620_COMMISSION_PAID",
    "M329700_AMORTIZATION_OF_INTANGIBLES",
    "M340070_SOFTWARE_MAINTENANCE",
    "M340120_COMMUNICATION_EXPENSES",
    "M340320_PROCESSING_COSTS",
    "M340420_STATIONARY_PRINTING",
    "M340520_MARKETING_AND_ADVERTISING",
    "M340600_OPERATIONAL_RISK_LOSSES",
    "M300701_MISCELLANEOUS_COSTS",
    "M300716_BANK_CHARGES_PENALTIES_AND_FINES",
    "M300705_MOTORING_EXPENSES",
    "M300707_DIRECTORS",
    "M300713_FX_AND_COST_HEDGE",
    "M340800_IC_OPERATING_EXPENSES",
    "M353000_INDIRECT_TAXES",
    "TAX"
  ]

  @value_keys [
    :january, :february, :march, :q1,
    :april, :may, :june, :q2,
    :july, :august, :september, :q3,
    :october, :november, :december, :q4,
    :ytd
  ]

  @doc """
  Generates the IFRS trend report data for the specified date.
  """
  # MisReports.Workers.ReportGens.Mis.IfrsTrend.generate_display("2025-03-31")
  def generate_display(to) do
    data = SourceData.get_ifrs_trend_data(to)
    format_data(data)
  end

  @doc """
  Helper function to create dynamic subtotals easily.

  ## Examples

      # Create a subtotal for specific keys
      create_dynamic_subtotal("Total Salaries", ["BASIC_PAY", "ALLOWANCE", "BONUSES"])

      # Create a subtotal with exclusions
      create_dynamic_subtotal("Net Income", ["ALL_INCOME"], exclude: ["EXCEPTIONAL_ITEMS"])

      # Create a subtotal combining other subtotals and keys
      create_dynamic_subtotal("Grand Total", ["SPECIFIC_KEY"], include: ["Other Subtotal"])
  """
  def create_dynamic_subtotal(label, keys, opts \\ []) do
    base_opts = [keys: keys]
    final_opts = Keyword.merge(base_opts, opts)
    {:subtotal, label, final_opts}
  end

  @doc """
  Helper to create common financial subtotals
  """
  def common_subtotals do
    %{
      staff_costs: create_dynamic_subtotal("Total Staff Costs",
        ["BASIC_PAY", "ALLOWANCE", "BONUSES", "PENSION", "NAPSA", "OVERTIME_PAY", "OTHER_BENEFITS"]),

      operating_expenses: create_dynamic_subtotal("Total Operating Expenses",
        ["OCCUPANCY", "EQUIPMENT", "DEPRECIATION", "EDUCATION_AND_TRAINING", "AUDIT_LEGAL_AND_PROFESSIONAL_FEES", "INSURANCE"]),

      interest_income: create_dynamic_subtotal("Total Interest Income",
        ["LOAN_ADVANCES_NORMAL_DEPOSITS", "FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS",
         "M305053_Interest_Income_treasury_bills", "M305054_INTEREST_INCOME_GOVT_BONDS",
         "LEASING_INCOME_FROM_NORMAL_DEPOSITS", "CREDIT_CARDS", "ALL_OTHER"]),

      fee_income: create_dynamic_subtotal("Total Fee Income",
        ["COMMISION_FEES", "FEES_FROM_FOREIGN_EXCHANGE"])
    }
  end

  @doc """
  Example of how to create a completely custom mapping with dynamic subtotals.
  This shows the power of the new system - subtotals can be positioned anywhere
  and reference any keys regardless of their position.
  """
  def example_dynamic_mapping do
    [
      # Executive summary at the top - references keys from throughout the report
      {:subtotal, "Executive Summary - Total Revenue",
       keys: ["LOAN_ADVANCES_NORMAL_DEPOSITS", "COMMISION_FEES", "TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES"]},

      {:subtotal, "Executive Summary - Total Costs",
       keys: ["BASIC_PAY", "OCCUPANCY", "DEPRECIATION", "INTEREST_TO_BANKS"]},

      # Regular groups can be in any order
      {"BASIC_PAY", "Basic Pay"},
      {"OCCUPANCY", "M328000  Premises Expenses"},

      # Mid-report subtotal referencing keys from different sections
      {:subtotal, "Operational Efficiency Ratio",
       keys: ["BASIC_PAY", "OCCUPANCY"],
       exclude: ["EXCEPTIONAL_ITEMS"]},

      {"LOAN_ADVANCES_NORMAL_DEPOSITS", "Interest income from Loans and advances"},
      {"COMMISION_FEES", "Commission Fees"},

      # Complex subtotal combining multiple sources
      {:subtotal, "Net Operating Income",
       include: ["Executive Summary - Total Revenue"],
       keys: ["SUNDRY_INCOME"],
       exclude: ["EXCEPTIONAL_ITEMS"]},

      {"TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES", "Trading income to be report in prudential"},
      {"INTEREST_TO_BANKS", "Interest paid to banks and financial institutions"},
      {"DEPRECIATION", "M329600  Depreciation Expenses"},
      {"SUNDRY_INCOME", "Sundry Income"},

      # Final summary using all previous subtotals
      {:subtotal, "Final Net Income",
       include: ["Net Operating Income", "Operational Efficiency Ratio"]}
    ]
  end

  def format_data(data) do
    # Two-pass processing for flexible subtotal positioning

    # Pass 1: Process all regular groups and build lookup map
    {regular_groups, group_lookup} = process_regular_groups(data)

    # Pass 2: Process mapping groups in order, handling subtotals with full context
    result = process_mapping_groups_with_subtotals(regular_groups, group_lookup)

    # Format numbers for display
    Enum.map(result, fn group ->
      %{
        group
        | total: Enum.into(group.total, %{}, fn {k, v} -> {k, format_and_wrap_number(v)} end),
          rows: Enum.map(group.rows, fn row ->
            Enum.into(row, %{}, fn
              {k, v} when k in @value_keys -> {k, format_and_wrap_number(v)}
              pair -> pair
            end)
          end)
      }
    end)
  end

  defp process_regular_groups(data) do
    # Extract only regular groups (non-subtotal entries)
    regular_entries = Enum.filter(@mapping_groups, fn
      {:subtotal, _} -> false
      {:subtotal, _, _} -> false
      _ -> true
    end)

    groups =
      Enum.map(regular_entries, fn {key, title} ->
        gls = Mappings.get_gl(key)
        rows = Enum.filter(data, fn row -> row.gl_no in gls end)
        total = sum_group_totals(rows, key)

        processed_rows =
          Enum.map(rows, fn row ->
            Enum.reduce(@value_keys, row, fn k, acc ->
              Map.update(acc, k, nil, fn v ->
                if key in @negative_keys and is_number_or_decimal(v) do
                  Decimal.mult(v, -1)
                else
                  v
                end
              end)
            end)
          end)

        processed_total =
          Enum.reduce(@value_keys, %{}, fn k, acc ->
            v =
              Map.get(total, k, nil)
              |> (fn v ->
                if key in @negative_keys and is_number_or_decimal(v), do: Decimal.mult(v, -1), else: v
              end).()

            Map.put(acc, k, v)
          end)

        %{
          key: key,
          title: title,
          total: processed_total,
          rows: processed_rows
        }
      end)

    # Create lookup map by key for efficient access
    group_lookup = Map.new(groups, fn group -> {group.key, group} end)

    {groups, group_lookup}
  end

  defp process_mapping_groups_with_subtotals(regular_groups, group_lookup) do
    {result, subtotal_lookup} =
      Enum.reduce(@mapping_groups, {[], %{}}, fn
        {:subtotal, label, opts}, {acc_result, acc_subtotals} ->
          subtotal = compute_flexible_subtotal(label, opts, group_lookup, acc_subtotals)
          updated_subtotals = Map.put(acc_subtotals, label, subtotal)
          {acc_result ++ [subtotal], updated_subtotals}

        {:subtotal, label}, {acc_result, acc_subtotals} ->
          # For backward compatibility - sum all groups processed so far
          groups_so_far = get_groups_processed_so_far(@mapping_groups, label, group_lookup, acc_subtotals)
          subtotal = compute_section_total(groups_so_far, label)
          updated_subtotals = Map.put(acc_subtotals, label, subtotal)
          {acc_result ++ [subtotal], updated_subtotals}

        {key, _title}, {acc_result, acc_subtotals} ->
          # Add the regular group at its position
          group = Map.get(group_lookup, key)
          if group do
            {acc_result ++ [group], acc_subtotals}
          else
            {acc_result, acc_subtotals}
          end
      end)

    result
  end

  defp compute_flexible_subtotal(label, opts, group_lookup, subtotal_lookup) do
    # Get groups to include - can be specific keys or other subtotals
    include_specs = Keyword.get(opts, :include, [])
    groups_to_sum = resolve_include_specs(include_specs, group_lookup, subtotal_lookup)

    # Get groups by keys if specified (this is the main dynamic feature)
    key_specs = Keyword.get(opts, :keys, [])
    groups_by_keys =
      key_specs
      |> Enum.map(fn key ->
        case Map.get(group_lookup, key) do
          nil ->
            # Log warning for missing keys but don't fail
            IO.warn("Key '#{key}' not found in group lookup for subtotal '#{label}'")
            nil
          group -> group
        end
      end)
      |> Enum.filter(& &1)

    # Combine all groups
    all_groups = groups_to_sum ++ groups_by_keys

    # Apply exclusions if specified
    exclude_specs = Keyword.get(opts, :exclude, [])
    final_groups = apply_exclusions(all_groups, exclude_specs, group_lookup, subtotal_lookup)

    compute_section_total(final_groups, label)
  end

  defp apply_exclusions(groups, [], _group_lookup, _subtotal_lookup), do: groups
  defp apply_exclusions(groups, exclude_specs, group_lookup, subtotal_lookup) do
    # Get groups/subtotals to exclude
    groups_to_exclude = resolve_include_specs(exclude_specs, group_lookup, subtotal_lookup)
    exclude_keys = Enum.map(groups_to_exclude, & &1.key) |> MapSet.new()

    # Filter out excluded groups
    Enum.filter(groups, fn group ->
      not MapSet.member?(exclude_keys, group.key)
    end)
  end

  defp resolve_include_specs(include_specs, group_lookup, subtotal_lookup) do
    Enum.flat_map(include_specs, fn spec ->
      cond do
        # If it's a subtotal reference
        Map.has_key?(subtotal_lookup, spec) ->
          [Map.get(subtotal_lookup, spec)]

        # If it's a group key reference
        Map.has_key?(group_lookup, spec) ->
          [Map.get(group_lookup, spec)]

        # Otherwise skip
        true ->
          []
      end
    end)
  end

  defp get_groups_processed_so_far(mapping_groups, current_label, group_lookup, _subtotal_lookup) do
    # For backward compatibility - get all regular groups that appear before this subtotal
    mapping_groups
    |> Enum.take_while(fn
      {:subtotal, ^current_label} -> false
      {:subtotal, ^current_label, _} -> false
      _ -> true
    end)
    |> Enum.filter(fn
      {:subtotal, _} -> false
      {:subtotal, _, _} -> false
      _ -> true
    end)
    |> Enum.map(fn {key, _title} -> Map.get(group_lookup, key) end)
    |> Enum.filter(& &1)
  end

  defp compute_section_total(section, label) do
    total =
      Enum.reduce(@value_keys, %{}, fn k, acc ->
        sum =
          section
          |> Enum.map(fn g ->
            v = g.total[k]
            cond do
              v == nil -> Decimal.new(0)
              is_binary(v) ->
                clean = v |> String.replace(",", "") |> String.replace("(", "-") |> String.replace(")", "")
                Decimal.new(clean)
              true -> v
            end
          end)
          |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

        Map.put(acc, k, sum)
      end)

    # Don't format here - let the main function handle formatting
    %{
      key: "SUBTOTAL_#{label |> String.replace(" ", "_") |> String.upcase()}",
      title: label,
      total: total,
      rows: []
    }
  end

  defp sum_group_totals(rows, _group_key) do
    Enum.reduce(@value_keys, %{}, fn key, acc ->
      total =
        rows
        |> Enum.map(&Map.get(&1, key, Decimal.new(0)))
        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

      Map.put(acc, key, total)
    end)
  end

  def format_and_wrap_number(val) do
    cond do
      is_binary(val) or val == nil ->
        val

      true ->
        rebased =
          val
          |> Decimal.div(Decimal.new(1000))
          |> Decimal.round(0)

        float = Decimal.to_float(rebased)
        formatted = Number.Delimit.number_to_delimited(float, [
          precision: 0,
          delimiter: ",",
          separator: "."
        ])

        if Decimal.compare(rebased, 0) == :lt do
          "(" <> String.replace(formatted, "-", "") <> ")"
        else
          formatted
        end
    end
  end

  defp is_number_or_decimal(val) do
    is_number(val) or match?(%Decimal{}, val)
  end

end
