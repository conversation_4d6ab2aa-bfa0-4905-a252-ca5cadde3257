defmodule MisReports.Workers.Sh01c do
   @moduledoc """
  Module for generating display data for MisReports Workers Sh01c.

  This module provides a function to generate display data.
  """
  alias MisReports.Workers.BalanceSheet
  alias MisReports.Utilities

  def generate_display(_from, to) do
    adj_values = MisReports.Utilities.get_adjustments_date(to)
    data = MisReports.SourceData.get_trial_balance_by_date_1c(to)

    fcy = MisReports.SourceData.get_fcy_amotized_bonds_1c(to)
    usd_rate = Utilities.get_exchange_rate_by_date_and_code(to, "USD")[:exchange_rate_lcy] || "1"
    counter_prty =  MisReports.SourceData.get_counter_party_securities(to)

    formatted_data = Enum.reduce(format(data, fcy, usd_rate, counter_prty), %{}, fn item, acc ->
      Map.put(acc, item.index, BalanceSheet.format_number(item.value))
    end)

    result = if Enum.empty?(adj_values) do
      formatted_data
    else
      formatted_adj_values = adj_values
      |> Enum.map(&Map.to_list/1)
      |> List.flatten()
      |> Enum.map(fn {k, v} ->
        v = if v == "", do: "0", else: v
        {k, BalanceSheet.format_number(v)}
      end)
      |> Enum.into(%{})

      Map.merge(formatted_data, formatted_adj_values, fn _key, old_val, new_val ->
        if new_val != "0", do: new_val, else: old_val
      end)
    end

    # result = sub_sums(result, ["Y16", "Y10"], "B16") #Y IS B16 GLS TOTAL THEN SUBTRACT FCY VALUE IN Y10
    # result = sub_sums(result, ["W16", "Z10"], "C16") #W IS C16 GLS TOTAL THEN SUBTRACT FCY VALUE IN Z10
    result = add_sums(result, ["B15", "B16", "B17", "B18"], "B19")
    result = add_sums(result, ["C15", "C16", "C17", "C18"], "C19")
    result = add_sums(result, ["D15", "D16"], "D19")
    result = add_sums(result, ["E15", "E16", "E17", "E18"], "E19")
    result = add_sums(result, ["C19"], "C62")
    result = add_sums(result, ["D19"], "D62")
    result = add_sums(result, ["E19", "E30"], "E62")
    result = add_sums(result, ["B38"], "B40")
    result = sub_sums(result, ["B28A", "B40"], "B28")
    result = add_sums(result, ["B27", "B28", "B29"], "B30")
    result = add_sums(result, ["C38"], "C40")
    result = add_sums(result, ["B19", "B24", "B30", "B40", "B61"], "B62")
    result = sub_sums(result, ["C28A", "C40"], "C28") #C28A is a placeholder value for C28, only using it to avoid conflicts with existing values
    result = add_sums(result, ["C27", "C28", "C29"], "C30")
    result = add_sums(result, ["D27", "D28", "D29"], "D30")
    result = add_sums(result, ["E27", "E28", "E29"], "E30")
    result = add_sums(result, ["F27", "F28", "F29"], "F30")

    result
    # |> IO.inspect(limit: :infinity)
  end

  defp add_sums(map, keys, target_key) do
    sum = Enum.reduce(keys, Decimal.new("0"), fn key, acc ->
      value =
        Map.get(map, key)
        |> case do
          nil -> "0"
          val -> val
        end
        |> String.replace(",", "")
        |> Decimal.new()

      Decimal.add(acc, value)
    end)

    Map.put(map, target_key, BalanceSheet.format_number(sum))
  end

  defp sub_sums(map, [first_key | rest_keys], target_key) do
    initial_value =
      Map.get(map, first_key)
      |> case do
        nil -> "0"
        val -> val
      end
      |> String.replace(",", "")
      |> Decimal.new()

    sum = Enum.reduce(rest_keys, initial_value, fn key, acc ->
      value =
        Map.get(map, key)
        |> case do
          nil -> "0"
          val -> val
        end
        |> String.replace(",", "")
        |> Decimal.new()

      Decimal.sub(acc, value)
    end)

    Map.put(map, target_key, format_number(sum))
  end

  def format(data, fcy, usd_rate, counter_prty) do
    get_costs_tbills(fcy) ++
    get_amortised_cost_tbills(fcy) ++
    get_fairvalue_pl_tbills(fcy) ++
    get_fairvalue_oci_tbills(fcy) ++
    get_amortised_cost_bonds(fcy) ++
    get_fair_value_bonds(fcy) ++
    get_fairvalue_oci_bonds(fcy) ++
    get_fcy_costs_tbills(fcy) ++
    get_fcy_costs_bonds(fcy, usd_rate) ++
    get_costs_bonds(fcy) ++
    get_gov_bonds(data) ++
    get_teasury_bills(data) ++
    get_all_other(data) ++
    get_total_bills_and_bonds(fcy) ++
    get_fair_value_tbills(data) ++
    get_amotised_gov_bonds(data) ++
    get_fair_val_gov_bonds(data) ++
    get_amotised_tbills(data) ++
    get_issued_by_govt_tbills(data) ++
    get_fair_val_tbills(data) ++
    get_bond_oci(data) ++
    get_fcy_bonds_cost(fcy, usd_rate) ++
    get_fcy_bonds_amotised_cost(fcy, usd_rate) ++
    get_issued_by_fcy_bonds_cost(fcy, usd_rate, counter_prty) ++
    get_issued_by_fcy_tbills_amortised(fcy, usd_rate, counter_prty) ++
    get_fairvalue_oci_fcy_bonds(fcy) ++
    get_fair_value_fcy_bonds(fcy) ++
    get_other_debt_sec_cost(fcy, usd_rate, counter_prty) ++
    get_other_debt_sec_amotised(fcy, usd_rate, counter_prty)
    # total_issued_by_gov = total_issued_by_gov(total_gov_bonds, total_teasury_bill)

    # total_issued_by_gov ++
    # get_foreign_bonds(data) ++
    # get_totals_from_trial_balance(trial_bal)
  end

  def get_costs_tbills(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "BondMMDiscount"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.clean_setlemt_amt)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "B15", value: format_number(value)} ]
  end

  def get_amortised_cost_tbills(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "BondMMDiscount" and deal.accbok == "AMORTIZED_COST"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.cary_val)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "C15", value: format_number(value)} ]
  end

  def get_fairvalue_pl_tbills(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "BondMMDiscount" and deal.accbok == "TRADING"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.mtm)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "D15", value: format_number(value)} ]
  end

  def get_fairvalue_oci_tbills(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "BondMMDiscount" and deal.accbok == "BANKING"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.mtm)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "E15", value: format_number(value)} ]
  end

  def get_costs_bonds(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "Bond"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.clean_setlemt_amt)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "B16", value: format_number(value)} ]
  end

  def get_amortised_cost_bonds(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "Bond" and deal.accbok == "AMORTIZED_COST"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.cary_val)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "C16", value: format_number(value)} ]
  end

  def get_fair_value_bonds(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "Bond" and deal.accbok == "TRADING"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.mtm)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "D16", value: format_number(value)} ]
  end

  def get_fairvalue_oci_bonds(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "ZMW" and deal.prod_type == "Bond" and deal.accbok in ["BANK NON-GM", "BANKING"]
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.mtm)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "E16", value: format_number(value)} ]
  end

  def get_total_bills_and_bonds(fcy) do
    treasury_bills = get_costs_tbills(fcy)
    govt_bonds = get_costs_bonds(fcy)

    tbills_value = treasury_bills
                   |> List.first()
                   |> Map.get(:value)
                   |> String.replace(",", "")
                   |> Decimal.new()

    bonds_value = govt_bonds
                  |> List.first()
                  |> Map.get(:value)
                  |> String.replace(",", "")
                  |> Decimal.new()
    [
      %{
        num: 4,
        index: "B19",
        value: Decimal.add(tbills_value, bonds_value)
      }
    ]
  end

  def get_fcy_costs_tbills(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "USD" and deal.prod_type == "BondMMDiscount"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.clean_setlemt_amt)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "B27", value: format_number(value)} ]
  end

  def get_fcy_costs_bonds(fcy, usd_rate) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "USD" and deal.prod_type == "Bond"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.clean_setlemt_amt)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()
    |> Decimal.mult(Decimal.new(usd_rate))

    [ %{num: 4, index: "B028", value: format_number(value)} ] #OLD VALUE
  end

  def get_issued_by_fcy_bonds_cost(fcy, usd_rate, counter_prty) do
    # Extract issuer names from counter_prty where security_type is "Bonds"
    bond_issuers =
      counter_prty
      |> Enum.filter(fn issuer ->
        issuer.foreign_and_other_debt_securities == "Foreign government" and
        issuer.security_type == "Bonds"
      end)
      |> Enum.map(fn issuer -> issuer.issuer_name end)

    value =
      fcy
      |> Enum.filter(fn deal ->
        deal.prod_type == "Bond" and
        deal.trade_ccy == "USD" and
        Enum.member?(bond_issuers, deal.issuer)
      end)
      |> Enum.reduce(Decimal.new(0), fn deal, acc ->
        Decimal.add(acc, deal.clean_setlemt_amt)
      end)
      |> Decimal.div(Decimal.new(1000))
      |> Decimal.mult(Decimal.new(usd_rate))
      |> Decimal.abs()

      #B28A is a placeholder value for B28, only using it to avoid conflicts with existing values
    [%{num: 4, index: "B28A", value: format_number(value)}]
  end

  def get_issued_by_fcy_tbills_amortised(fcy, usd_rate, counter_prty) do
    # Extract issuer names from counter_prty where security_type is "Bonds"
    bond_issuers =
      counter_prty
      |> Enum.filter(fn issuer ->
        issuer.foreign_and_other_debt_securities == "Foreign government" and
        issuer.security_type == "Bonds"
      end)
      |> Enum.map(fn issuer -> issuer.issuer_name end)

    value =
      fcy
      |> Enum.filter(fn deal ->
        deal.prod_type == "Bond" and
        deal.trade_ccy == "USD" and
        deal.accbok == "AMORTIZED_COST" and
        Enum.member?(bond_issuers, deal.issuer)
      end)
      |> Enum.reduce(Decimal.new(0), fn deal, acc ->
        Decimal.add(acc, deal.cary_val)
      end)
      |> Decimal.div(Decimal.new(1000))
      |> Decimal.mult(Decimal.new(usd_rate))
      |> Decimal.abs()

      # C28A is a placeholder value for C28, only using it to avoid conflicts with existing values
    [%{num: 4, index: "C28A", value: format_number(value)}]
  end

  def get_fairvalue_oci_fcy_bonds(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "USD" and deal.prod_type == "Bond" and deal.accbok in ["BANK NON-GM", "BANKING"]
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.mtm)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "D28", value: format_number(value)} ]
  end

  def get_fair_value_fcy_bonds(fcy) do
    value = fcy
    |> Enum.filter(fn deal ->
      deal.trade_ccy == "USD" and deal.prod_type == "Bond" and deal.accbok == "TRADING"
    end)
    |> Enum.reduce(Decimal.new(0), fn deal, acc ->
      Decimal.add(acc, deal.mtm)
    end)
    |> Decimal.div(Decimal.new(1000))
    |> Decimal.abs()

    [ %{num: 4, index: "E28", value: format_number(value)} ]
  end

  def get_other_debt_sec_cost(fcy, usd_rate, counter_prty) do
    # Extract issuer names from counter_prty where:
    # - foreign_and_other_debt_securities is "Other debt securities"
    # - security_type is "Bonds"
    bond_issuers =
      counter_prty
      |> Enum.filter(fn issuer ->
        issuer.foreign_and_other_debt_securities == "Other debt securities" and
        issuer.security_type == "Bonds"
      end)
      |> Enum.map(fn issuer -> issuer.issuer_name end)

    value =
      fcy
      |> Enum.filter(fn deal ->
        deal.prod_type == "Bond" and
        deal.trade_ccy == "USD" and
        Enum.member?(bond_issuers, deal.issuer)
      end)
      |> Enum.reduce(Decimal.new(0), fn deal, acc ->
        Decimal.add(acc, deal.clean_setlemt_amt)
      end)
      |> Decimal.div(Decimal.new(1000))
      |> Decimal.mult(Decimal.new(usd_rate))
      |> Decimal.abs()

    [%{num: 4, index: "B38", value: format_number(value)}]
  end

  def get_other_debt_sec_amotised(fcy, usd_rate, counter_prty) do
    # Extract issuer names from counter_prty where:
    # - foreign_and_other_debt_securities is "Other debt securities"
    # - security_type is "Bonds"
    bond_issuers =
      counter_prty
      |> Enum.filter(fn issuer ->
        issuer.foreign_and_other_debt_securities == "Other debt securities" and
        issuer.security_type == "Bonds"
      end)
      |> Enum.map(fn issuer -> issuer.issuer_name end)

    value =
      fcy
      |> Enum.filter(fn deal ->
        deal.prod_type == "Bond" and
        deal.trade_ccy == "USD" and
        deal.accbok == "AMORTIZED_COST" and
        Enum.member?(bond_issuers, deal.issuer)
      end)
      |> Enum.reduce(Decimal.new(0), fn deal, acc ->
        Decimal.add(acc, deal.cary_val)
      end)
      |> Decimal.div(Decimal.new(1000))
      |> Decimal.mult(Decimal.new(usd_rate))
      |> Decimal.abs()

    [%{num: 4, index: "C38", value: format_number(value)}]
  end



  # OLD WORKINGS
  def get_teasury_bills(data) do
    [
      %{num: 4, index: "T15", value: total_amt(data, ["41240", "13100"])}, #removed "41200", "40165"
    ]
  end

  def get_gov_bonds(data) do
    [
      %{num: 4, index: "Y16", value: total_amt(data, ["40045", "40680", "40728", "57605"])}, #subtract from Y10 to get B16
    ]
  end  #"40045", "41860", "40680", "59200", "40728", "41480", "57605", "40030", "100685"


  def get_all_other(data) do
    [
      %{num: 4, index: "B61", value: total_amt(data, ["131500"])},
    ]
  end

  def get_fair_value_tbills(data) do
    [
      %{num: 4, index: "DO15", value: total_amt(data, ["M105376"])}, #removed "M105371"
    ]
  end

  def get_fair_val_gov_bonds(data) do
    [
      %{num: 4, index: "DO16", value: total_amt(data, ["M105323"])}, #REMOVED M105321
    ]
  end

  def get_amotised_tbills(data) do
    [
      %{num: 4, index: "H15", value: total_amt(data, ["M101233"])}, #removed M101234
    ]
  end

  def get_amotised_gov_bonds(data) do
    [
      %{num: 4, index: "W16", value: total_amt(data, ["M105140", "M105174"])},
    ]
  end

  # def get_issued_by_fcy_govt(data) do
  #   [
  #    %{num: 4, index: "16", value: total_amt(data, ["57605"])}
  #   ]
  # end

  def get_issued_by_govt_tbills(data) do
    [
      %{num: 4, index: "C027", value: total_amt(data, ["M101233"])}, #OLD C27
    ]
  end

  # def get_issued_by_govt_bonds(data) do
  #   "M105140"
  # end

  def get_fair_val_tbills(data) do
    [
      %{num: 4, index: "EO15", value: total_amt(data, ["M105129"])},  #removed M105126 #EO refers to old E value
    ]
  end

  def get_bond_oci(data) do
    [
      %{num: 4, index: "EO16", value: total_amt(data, ["M105126"])},  #removed M105126
    ]
  end

  def get_fcy_bonds_cost(fcy, usd_rate) do
    case fcy do
      [%{clean_setlemt_amt: clean_setlemt_amt} | _] ->

        clean_setlemt_amt = if is_nil(clean_setlemt_amt), do: Decimal.new("0.0"), else: clean_setlemt_amt
        usd_rate = if is_nil(usd_rate), do: Decimal.new("0.0"), else: usd_rate

        value = Decimal.mult(clean_setlemt_amt, usd_rate)

        [
          %{num: 4, index: "Y10", value: format_amt(value)}
        ]

      _ ->
        []
    end
  end

  def get_fcy_bonds_amotised_cost(fcy, usd_rate) do
    case fcy do
      [%{cary_val: cary_val} | _] ->

        cary_val = if is_nil(cary_val), do: Decimal.new("0.0"), else: cary_val
        usd_rate = if is_nil(usd_rate), do: Decimal.new("0.0"), else: usd_rate

        value = Decimal.mult(cary_val, usd_rate)

        [%{num: 4, index: "Z10", value: format_amt(value)}]

      _ ->
        []
    end
  end

  def format_amt(amt) do
    case amt do
      "0.0" -> 0.0
       0.0 -> 0.0
       0 -> 0
      nil -> 0.0
      _ ->
        Decimal.abs(amt)
        |> Decimal.div(Decimal.new("1000"))
        |> Decimal.round(0)
    end
  end

  def format_number(val) do
    case is_binary(val) or val == nil do
      true -> val
      false ->
        v = to_string(val)|> Decimal.new()
        float = Decimal.to_float(v)
        Number.Delimit.number_to_delimited(float, [
          precision: 0,
          delimiter: ",",
          separator: "."
        ])
    end
  end

  def total_amt(data, gls)do
    Stream.filter(data, fn item -> item.gl_no in gls end)
    |> Enum.to_list()
    |> Enum.reduce("0.0", fn item, acc -> Decimal.add(acc, item.actual_this_year) end)
    |> case do
      "0.0" -> "0.0"
      sum ->
        Decimal.abs(sum)
        |> Decimal.div(Decimal.new("1000"))
        |> Decimal.round(0)
    end
  end

  # def get_teasury_bills(data) do
  #   [
  #     %{num: 1, index: "B15", value: total_teasury_bills(data)},
  #   ]
  # end

  # def total_teasury_bills(data)do
  #   data
  #   |> Stream.filter(fn item -> item.prod_type in ["BondMMDiscount"] end)
  #   |> Enum.to_list()
  #   |> Enum.reduce("0.0", fn item, acc -> Decimal.add(acc, item.clean_setlemt_amt) end)
  #   |> case do
  #     "0.0" -> "0.0"
  #     sum ->
  #       Decimal.abs(sum)
  #       |> Decimal.div(Decimal.new("1000"))
  #       |> Decimal.round(0)
  #   end
  # end

  # def get_gov_bonds(data) do
  #   [
  #     %{num: 2, index: "B16", value: total_gov_bonds(data)},
  #   ]
  # end

  # def total_gov_bonds(data)do
  #   data
  #   |> Stream.filter(fn item -> item.prod_type in ["Bond"] end)
  #   |> Enum.to_list()
  #   |> Enum.reduce("0.0", fn item, acc -> Decimal.add(acc, item.clean_setlemt_base) end)
  #   |> case do
  #     "0.0" -> "0.0"
  #     sum ->
  #       Decimal.abs(sum)
  #       |> Decimal.div(Decimal.new("1000"))
  #       |> Decimal.round(0)
  #   end
  # end

  # def get_foreign_bonds(data) do
  #   [
  #     %{num: 3, index: "B28", value: total_foreign_bonds(data)}
  #   ]
  # end

  # def total_foreign_bonds(data) do
  #   Stream.filter(data, fn item -> item.prod_type in ["Bond"] and item.trade_ccy == "USD" end)
  #   |> Enum.to_list()
  #   |> Enum.reduce("0.0", fn item, acc -> Decimal.add(acc, item.clean_setlemt_amt) end)
  #   |> case do
  #     "0.0" -> "0.0"
  #     sum -> Decimal.abs(sum)
  #   end
  # end

  # def total_issued_by_gov(total_gov_bonds, total_teasury_bill) do
  #   [total_gov_bonds | _] = total_gov_bonds
  #   [total_teasury_bill | _] = total_teasury_bill
  #   total = Decimal.add(total_gov_bonds.value, total_teasury_bill.value)

  #   [
  #     %{num: 2, index: "B19", value: total}
  #   ]
  # end

  # def get_totals_from_trial_balance(data) do
  #   data =
  #     data
  #     |> Stream.filter(fn item -> item.gl_no in ["M101234", "M105371","M105140","M105321", "131500"] end)
  #     |> Enum.to_list()

  #     [
  #       %{num: 2, index: "C15", value: get_trial_bal_total_by_gl_no(data, ["M101234"])},
  #       %{num: 2, index: "D15", value: get_trial_bal_total_by_gl_no(data, ["M105371"])},
  #       %{num: 2, index: "C16", value: get_trial_bal_total_by_gl_no(data, ["M105140"])},
  #       %{num: 2, index: "D16", value: get_trial_bal_total_by_gl_no(data, ["M105321"])},
  #       %{num: 2, index: "C19", value: get_trial_bal_total_by_gl_no(data, ["M101234", "M105140"])},
  #       %{num: 2, index: "D19", value: get_trial_bal_total_by_gl_no(data, ["M105371", "M105321"])},
  #       %{num: 2, index: "B61", value: get_trial_bal_total_by_gl_no(data, ["131500"])}
  #     ]
  # end

  # def get_trial_bal_total_by_gl_no(data, gl) do
  #   Enum.filter(data, fn item -> item.gl_no in gl end)
  #   |> Enum.reduce("0.0", fn item, acc -> Decimal.add(acc, item.actual_this_year) end)
  #   |> case do
  #     "0.0" -> "0.0"
  #     sum ->
  #       Decimal.abs(sum)
  #       |> Decimal.div(Decimal.new("1000"))
  #       |> Decimal.round(0)
  #   end
  # end

end
