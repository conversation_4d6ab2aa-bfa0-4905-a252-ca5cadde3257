defmodule MisReports.Workers.Sh15 do
    @moduledoc """
  Module for generating display data for MisReports Workers sh15.

  This module provides a function to generate display data.
  """
  alias MisReports.Workers.{Sh12, Sh32a, BalanceSheet, Sh14}

  def generate_display(shd13, blc_sheet, date, adjustments) do
    shd12 = Sh12.generate_display(date, date)
    shd32a = Sh32a.generate_display(date, date)
    value_32 = shd32a.total.total_zmw
    sh14_val = Sh14.get_display_output(blc_sheet, date, adjustments)

    val_62 = Decimal.new(520000)

    formatted_data = format(shd13, shd12, value_32, val_62, sh14_val)

    result = Enum.reduce(formatted_data, shd12, fn item, acc ->
      Map.put(acc, item.index, BalanceSheet.format_number(item.value))
    end)

    Map.put(result, "C50", value_32)
  end

  def format(shd13, shd12, value_32, val_62, sh14_val) do
    get_statutory_reserves(shd13) ++
    get_retained_earnings(shd13) ++
    get_fair_val_reserves(shd13) ++
    get_revaluation_reserves(shd13) ++
    get_shd12_value(shd12) ++
    add_total(shd13, shd12) ++
    get_unrealized_gains(shd13) ++
    subt_values(shd13, shd12) ++
    get_shd32a_value(value_32) ++
    secondary_capt_total(shd13, value_32) ++
    compare_and_store(shd13, shd12, value_32) ++
    total_regulatory_capital(shd13, shd12, value_32) ++
    get_prescribed_nominal_val(val_62) ++
    get_shd14_val(sh14_val) ++
    excess_difficiency(shd13, shd12, value_32, sh14_val) ++
    total_c47_c56(shd13, shd12, value_32)
    # get_min_capital_value(val_c38)
    # total_excess_deficiency(shd13, shd12, value_32)
  end

  def get_min_capital_value(val_c38) do

    new_val = Decimal.new(val_c38)
    revalued_amount = Decimal.mult(new_val, Decimal.new("0.1"))
    formatted_value = BalanceSheet.format_number(revalued_amount)

    [%{num: 1, index: "C38", value: formatted_value}]
  end

  def get_statutory_reserves(shd13) do
    value = Map.get(shd13, "F39", "0.0")
    [
      %{num: 1, index: "C26", value: BalanceSheet.format_number(value)},
    ]
  end

  def get_prescribed_nominal_val(val_62) do
    [
      %{num: 1, index: "C62", value: BalanceSheet.format_number(val_62)},
    ]
  end

  def get_retained_earnings(shd13) do
    value = Map.get(shd13, "C39", "0.0")
    [
      %{num: 1, index: "C24", value: BalanceSheet.format_number(value)},
    ]
  end

  def get_fair_val_reserves(shd13) do
    value = Map.get(shd13, "D39", "0.0")
    [
      %{num: 1, index: "C25", value: BalanceSheet.format_number(value)},
    ]
  end

  def get_unrealized_gains(shd13) do
    value = Map.get(shd13, "D39", "0.0")
    [
      %{num: 1, index: "C35", value: BalanceSheet.format_number(value)},
    ]
  end

  def get_revaluation_reserves(shd13) do
    value = Map.get(shd13, "E39", "0.0")
    value = String.replace(value, ",", "")

    new_val = Decimal.new(value)
    revalued_amount = Decimal.mult(new_val, Decimal.new("0.4"))
    formatted_value = BalanceSheet.format_number(revalued_amount)

    [%{num: 1, index: "C53", value: formatted_value}]
  end

  def get_shd12_value(shd12) do
    value = Map.get(shd12, "C17", "0.0")
    [
      %{num: 1, index: "C21", value: BalanceSheet.format_number(value)},
    ]
  end

  def get_shd32a_value(value_32) do
    [
      %{num: 1, index: "C50", value: BalanceSheet.format_number(value_32)}
    ]
  end

  def add_total(shd13, shd12) do
    statutory_reserves = get_statutory_reserves(shd13)
    retained_earnings = get_retained_earnings(shd13)
    fair_val_reserves = get_fair_val_reserves(shd13)
    shd12_value = get_shd12_value(shd12)

    total_value = Enum.reduce([statutory_reserves, retained_earnings, fair_val_reserves, shd12_value], 0, fn items, acc ->
      value = items |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()
      Decimal.add(acc, value)
    end)

    formatted_total = BalanceSheet.format_number(total_value)
    [%{num: 1, index: "C28", value: formatted_total}]
  end

  def secondary_capt_total(shd13, value_32) do
    revaluation_reserves = get_revaluation_reserves(shd13)
    val_32 = get_shd32a_value(value_32)

    total_value = Enum.reduce([revaluation_reserves, val_32], 0, fn items, acc ->
      value = items |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()
      Decimal.add(acc, value)
    end)

    formatted_total = BalanceSheet.format_number(total_value)
    [%{num: 1, index: "C55", value: formatted_total}]
  end

  def subt_values(shd13, shd12) do
    total_value = add_total(shd13, shd12) |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()
    fair_val_reserves = get_fair_val_reserves(shd13) |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()

    result = Decimal.sub(total_value, fair_val_reserves)

    formatted_result = BalanceSheet.format_number(result)
    [%{num: 1, index: "C47", value: formatted_result}]
  end

  def compare_and_store(shd13, shd12, value_32) do
    secondary_capt_total_value = secondary_capt_total(shd13, value_32) |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()
    subt_values_result = subt_values(shd13, shd12) |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()

    result_value = cond do
      # IF(AND(C55>=0,C47<0),0 If C55 is greater than or equal to 0 and C47 is less than 0, return 0
      Decimal.compare(secondary_capt_total_value, 0) in [:eq, :gt] and Decimal.compare(subt_values_result, 0) == :lt ->
        Decimal.new(0)

      # IF(C55>=C47,C47,C55) If C55 is greater than or equal to C47, return C47
      Decimal.compare(secondary_capt_total_value, subt_values_result) in [:eq, :gt] ->
        subt_values_result

      true ->
        secondary_capt_total_value
    end

    formatted_result = BalanceSheet.format_number(result_value)
    [%{num: 1, index: "C56", value: formatted_result}]
  end

  def total_regulatory_capital(shd13, shd12, value_32) do
    total_primary_capital = subt_values(shd13, shd12)
    total_elig_capital = compare_and_store(shd13, shd12, value_32)

    total_value = Enum.reduce([total_primary_capital, total_elig_capital], 0, fn items, acc ->
      value = items |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()
      Decimal.add(acc, value)
    end)

    formatted_total = BalanceSheet.format_number(total_value)
    [%{num: 1, index: "C61", value: formatted_total}]
  end

  def get_shd14_val(sh14_val) do
    c62_val = Decimal.new(520000)
    f82_val = Map.get(sh14_val, "F82", "0.0") |> to_decimal()
    ten_percent_f82 = Decimal.mult(f82_val, Decimal.new("0.1"))

    value =
      if Decimal.cmp(ten_percent_f82, c62_val) == :gt do #IF(10%*'Schedule 14'!F82>C62,10%*'Schedule 14'!F82,C62)
        ten_percent_f82
      else
        c62_val
      end

    [
      %{num: 1, index: "C58", value: BalanceSheet.format_number(value)},
    ]
  end

  def total_c47_c56(shd13, shd12, value_32) do
    c47_val = subt_values(shd13, shd12)
    c56_val = compare_and_store(shd13, shd12, value_32)

    total_value = Enum.reduce([c47_val, c56_val], 0, fn items, acc ->
      value = items |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()
      Decimal.add(acc, value)
    end)

    formatted_total = BalanceSheet.format_number(total_value)
    [%{num: 1, index: "D60", value: formatted_total}]
  end

  def excess_difficiency(shd13, shd12, value_32, sh14_val) do
    val = total_c47_c56(shd13, shd12, value_32)
    c58_val = get_shd14_val(sh14_val)

    val_decimal = val |> hd() |> Map.get(:value) |> to_decimal()
    c58_val_decimal = c58_val |> hd() |> Map.get(:value) |> to_decimal()

    total_value = Decimal.sub(val_decimal, c58_val_decimal)

    formatted_total = BalanceSheet.format_number(total_value)
    [%{num: 1, index: "C60", value: formatted_total}]
  end

  defp to_decimal(value) when is_nil(value), do: Decimal.new("0.0")
  defp to_decimal(value), do: value |> String.replace(",", "") |> Decimal.new()

  def format_number(val) do
    case val do
      "0.0"-> ""
      _an ->
        v = to_string(val)|> Decimal.new()
        float = Decimal.to_float(v)
        Number.Delimit.number_to_delimited(float, [
          precision: 2,
          delimiter: ",",
          separator: "."
        ])
        |> case do
          "0.00"-> "0"
          num -> String.replace_trailing(num, ".00", "")
        end
    end
  end

  # def total_excess_deficiency(shd13, shd12, value_32) do
  #   total_reg_capital = total_regulatory_capital(shd13, shd12, value_32)
  #   total_mini_cap = 1942257

  #   total_value = Enum.reduce([total_reg_capital, total_mini_cap], 0, fn items, acc ->
  #     value = items |> hd() |> Map.get(:value) |> String.replace(",", "") |> Decimal.new()
  #     Decimal.sub(acc, value)
  #   end)

  #   formatted_total = BalanceSheet.format_number(total_value)
  #   [%{num: 1, index: "C60", value: formatted_total}]
  # end

end
